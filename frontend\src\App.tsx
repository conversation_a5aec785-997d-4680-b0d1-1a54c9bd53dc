import { useState, useEffect } from 'react';
import { Container } from 'react-bootstrap';
import NavBar from './components/NavBar/NavBar';
import Sidebar from './components/Sidebar/Sidebar';
import MapComponent from './components/Map/MapComponent';
import AnalyticsDashboard from './components/Analytics/AnalyticsDashboard';
import ToolsPanel from './components/Tools/ToolsPanel';
import RegionalFilterModal from './components/RegionalFilter/RegionalFilterModal';
import DownloadModal from './components/Download/DownloadModal';
import { useDiscoveryLayers } from './hooks/useDiscoveryLayers';
import 'bootstrap/dist/css/bootstrap.min.css';
import './App.css';

function App() {
  const { layers, selectedLayers, handleLayerToggle, isLoading, error } = useDiscoveryLayers();

  const [currentView, setCurrentView] = useState<'map' | 'analytics'>('map');
  const [isToolsPanelOpen, setIsToolsPanelOpen] = useState(false);

  const [dateRange, setDateRange] = useState({
    startDate: '2025/05/20',
    endDate: '2025/05/20'
  });
  const [drawnItems, setDrawnItems] = useState<any>(null);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Layer opacity state
  const [layerOpacities, setLayerOpacities] = useState<{ [layerName: string]: number }>({});

  const handleOpacityChange = (layerName: string, opacity: number) => {
    setLayerOpacities(prev => ({
      ...prev,
      [layerName]: opacity
    }));
  };

  // AOI state management
  const [isDrawingMode, setIsDrawingMode] = useState(false);
  const [hasDrawnArea, setHasDrawnArea] = useState(false);

  // Regional AOI state management
  const [aoiMethod, setAOIMethod] = useState<'drawn' | 'regional'>('drawn');
  const [hasRegionalSelection, setHasRegionalSelection] = useState(false);
  const [showRegionalModal, setShowRegionalModal] = useState(false);
  const [regionalAOI, setRegionalAOI] = useState<any>(null);

  // AOI Preview data for sidebar (both drawn and administrative boundaries)
  const [aoiPreviewData, setAoiPreviewData] = useState<any>(null);

  // Download modal state
  const [showDownloadModal, setShowDownloadModal] = useState(false);

  // Basemap state management
  const [selectedBasemap, setSelectedBasemap] = useState('osm:osm'); // Default to OpenStreetMap

  // Coordinate pin mode state
  const [coordinatePinMode, setCoordinatePinMode] = useState(false);
  const [currentCoordinates, setCurrentCoordinates] = useState('');

  // Legend user mode state
  const [legendUserMode, setLegendUserMode] = useState<'simple' | 'advanced'>('simple');

  // Legend panel visibility state
  const [showLegendPanel, setShowLegendPanel] = useState(false);
  const [selectedLegendLayerName, setSelectedLegendLayerName] = useState<string | null>(null);

  // Interactive boundary highlighting state
  const [highlightedBoundaries, setHighlightedBoundaries] = useState<GeoJSON.Feature[]>([]);

  const handleDateChange = (type: 'startDate' | 'endDate', value: string) => {
    setDateRange(prev => ({ ...prev, [type]: value }));
  };

  const handleSearch = (query: string) => {
    console.log('Searching for:', query);
  };

  const handlePreviewData = () => {
    alert(`🔍 Data Preview Requested!\n\nLayers: ${selectedLayers.join(', ') || 'None'}\nDate Range: ${dateRange.startDate} to ${dateRange.endDate}\n\nCheck the console for details.`);
    console.log('Previewing data for layers:', selectedLayers);
    console.log('Date range:', dateRange);
    console.log('Region:', drawnItems);
  };

  const handleDownloadData = () => {
    console.log('Downloading data for layers:', selectedLayers);
    console.log('Date range:', dateRange);
    console.log('Region:', drawnItems);
    setShowDownloadModal(true);
  };

  const handleQueryTemporalData = () => {
    alert(`🔍 Temporal Query Executed!\n\nLayers: ${selectedLayers.join(', ') || 'None'}\nDate Range: ${dateRange.startDate} to ${dateRange.endDate}\n\nCheck the console for details.`);
    console.log('Temporal layers selected:', selectedLayers);
    console.log('Date range:', dateRange);
    console.log('Region:', drawnItems);
  };

  const handleToolsToggle = () => {
    setIsToolsPanelOpen(prev => !prev);
  };

  const handleToolsPanelClose = () => {
    setIsToolsPanelOpen(false);
  };

  // Reset all application state (for Home button)
  const resetAllStates = () => {
    console.log('🏠 Home button clicked - Resetting all application states');
    
    // Reset layer selections
    selectedLayers.forEach(layerName => {
      handleLayerToggle(layerName); // Deselect all layers (toggle will turn them off if currently on)
    });
    
    // Reset date range
    setDateRange({
      startDate: '2025/05/20',
      endDate: '2025/05/20'
    });
    
    // Reset drawing states
    setIsDrawingMode(false);
    setHasDrawnArea(false);
    setDrawnItems(null);
    
    // Reset AOI states
    setAOIMethod('drawn');
    setHasRegionalSelection(false);
    setRegionalAOI(null);
    setAoiPreviewData(null);
    
    // Reset coordinate pin mode
    setCoordinatePinMode(false);
    setCurrentCoordinates('');
    
    // Reset basemap to default
    setSelectedBasemap('osm:osm');
    
    // Reset legend states
    setLegendUserMode('simple');
    setShowLegendPanel(false);
    setSelectedLegendLayerName(null);
    
    // Clear highlighted boundaries
    setHighlightedBoundaries([]);
    
    // Reset layer opacities
    setLayerOpacities({});
    
    // Close all modals
    setShowRegionalModal(false);
    setShowDownloadModal(false);
    setIsToolsPanelOpen(false);
    
    // Reset view to map
    setCurrentView('map');
    
    console.log('✅ Application state reset complete - returned to default South Africa view');
  };

  // AOI handlers
  const handleDrawModeToggle = (isDrawing: boolean) => {
    setIsDrawingMode(isDrawing);
    
    // Clear pin mode when drawing is activated
    if (isDrawing) {
      setCoordinatePinMode(false);
      setCurrentCoordinates('');
    }
  };

  const handleClearDrawnArea = () => {
    setHasDrawnArea(false);
    setDrawnItems(null);
  };

  const handleAOIComplete = (aoiData: any, dateRange: any) => {
    console.log('AOI Complete:', aoiData, dateRange);
    setHasDrawnArea(true);
    // Here you would typically process the AOI data for download
  };

  // Handle AOI preview data from both drawn polygons and administrative boundaries
  const handleAOIPreview = (aoiData: any) => {
    console.log('🔍 App: handleAOIPreview called with:', aoiData);
    console.log('🔍 App: Current aoiPreviewData:', aoiPreviewData);

    // Check if the AOI data is actually different - comprehensive comparison
    const currentData = aoiPreviewData;
    const newData = aoiData;

    // Handle null cases first
    if (!currentData && !newData) {
      console.log('🔍 App: Both AOI data are null, skipping update');
      return;
    }

    if (!currentData && newData) {
      console.log('🔍 App: Setting initial AOI data');
      setAoiPreviewData(aoiData);
      return;
    }

    if (currentData && !newData) {
      console.log('🔍 App: Clearing AOI data (reset to South Africa)');
      setAoiPreviewData(null);
      return;
    }

    if (currentData && newData) {
      // Compare key identifying properties, not just bounds
      const typeChanged = currentData.type !== newData.type;
      const levelChanged = currentData.level !== newData.level;
      const nameChanged = currentData.name !== newData.name;
      const codeChanged = currentData.code !== newData.code;

      // Compare selection details for administrative boundaries
      const selectionChanged = JSON.stringify(currentData.selectionDetails) !== JSON.stringify(newData.selectionDetails);

      // Compare bounds with tolerance for floating point precision
      const currentBounds = currentData.bounds;
      const newBounds = newData.bounds;
      let boundsChanged = false;

      if (currentBounds && newBounds) {
        const tolerance = 0.001; // Small tolerance for floating point comparison
        boundsChanged = (
          Math.abs(currentBounds.north - newBounds.north) > tolerance ||
          Math.abs(currentBounds.south - newBounds.south) > tolerance ||
          Math.abs(currentBounds.east - newBounds.east) > tolerance ||
          Math.abs(currentBounds.west - newBounds.west) > tolerance
        );
      } else {
        boundsChanged = currentBounds !== newBounds; // One is null/undefined
      }

      const hasSignificantChange = typeChanged || levelChanged || nameChanged || codeChanged || boundsChanged || selectionChanged;

      console.log('🔍 App: AOI change analysis:', {
        typeChanged,
        levelChanged,
        nameChanged,
        codeChanged,
        boundsChanged,
        selectionChanged,
        hasSignificantChange,
        currentLevel: currentData.level,
        newLevel: newData.level,
        currentName: currentData.name,
        newName: newData.name,
        currentSelection: currentData.selectionDetails,
        newSelection: newData.selectionDetails
      });

      if (!hasSignificantChange) {
        console.log('🔍 App: AOI data unchanged, skipping update to prevent unnecessary re-zoom');
        return;
      }
    }

    console.log('🔍 App: Setting new AOI preview data');
    setAoiPreviewData(aoiData);
  };

  // Handle AOI download from sidebar preview card
  const handleAOIDownload = (selectedLayers: string[], aoiData: any) => {
    console.log('Downloading AOI data:', { selectedLayers, aoiData });
    // Store AOI and layers for download modal
    setAoiPreviewData(aoiData);
    // Force close the legend panel
    setShowLegendPanel(false);
    setTimeout(() => {
      // Show download modal after a slight delay to ensure legend panel is closed
      setShowDownloadModal(true);
    }, 100);
  };

  const handleBasemapChange = (basemapName: string) => {
    setSelectedBasemap(basemapName);
    console.log('Basemap changed to:', basemapName);
  };

  // Handle coordinate pin mode toggle
  const handleCoordinatePinModeToggle = (enabled: boolean) => {
    console.log('handleCoordinatePinModeToggle called with:', enabled);
    setCoordinatePinMode(enabled);
    
    // Clear other AOI methods when pin mode is enabled
    if (enabled) {
      setHasDrawnArea(false);
      setIsDrawingMode(false);
      setHasRegionalSelection(false);
      setRegionalAOI(null);
      setAoiPreviewData(null);
      console.log('Pin mode enabled - cleared other AOI methods');
    }
    
    console.log('Coordinate pin mode:', enabled ? 'enabled' : 'disabled');
  };

  // Handle map click for coordinate pin
  const handleMapClick = (latlng: {lat: number, lng: number}) => {
    if (coordinatePinMode) {
      // Format coordinates to 6 decimal places
      const formattedCoordinates = `${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`;
      setCurrentCoordinates(formattedCoordinates);
      setCoordinatePinMode(false); // Turn off pin mode after placing
      console.log('Coordinates set to:', formattedCoordinates);
    }
  };

  // Handle layer info - show legend panel and trigger info modal
  const handleShowLayerInfo = (layerName: string) => {
    setSelectedLegendLayerName(layerName);
    setShowLegendPanel(true);
    // The legend panel will handle showing the layer info modal
  };

  const handleCloseLegendPanel = () => {
    setShowLegendPanel(false);
    setSelectedLegendLayerName(null);
  };

  // Regional AOI handlers
  const handleAOIMethodChange = (method: 'drawn' | 'regional') => {
    setAOIMethod(method);

    // Clear existing selections when switching methods
    if (method === 'drawn') {
      setHasRegionalSelection(false);
    } else {
      setHasDrawnArea(false);
      setIsDrawingMode(false);
    }

    // Clear pin mode when switching to other AOI methods
    setCoordinatePinMode(false);
    setCurrentCoordinates('');

    console.log('AOI method changed to:', method);
  };

  const handleConfigureRegions = () => {
    console.log('Configure regions clicked - opening modal');
    setShowRegionalModal(true);
  };

  const handleClearRegionalSelection = () => {
    setHasRegionalSelection(false);
    setRegionalAOI(null);
    console.log('Regional selection cleared');
  };

  const handlePredefinedPolygon = (size: string) => {
    console.log('Creating predefined polygon:', size);

    // Parse size (e.g., "25x25" -> 25km x 25km)
    const [width, height] = size.split('x').map(Number);
    const sizeKm = width; // Assuming square polygons

    // Create a polygon centered on South Africa (approximate center)
    const centerLat = -29.0; // South Africa approximate center
    const centerLng = 24.0;

    // Convert km to degrees (rough approximation: 1 degree ≈ 111 km)
    const degreeOffset = (sizeKm / 2) / 111;

    const bounds = {
      north: centerLat + degreeOffset,
      south: centerLat - degreeOffset,
      east: centerLng + degreeOffset,
      west: centerLng - degreeOffset
    };

    // Calculate area in km²
    const area = sizeKm * sizeKm;

    // Set as drawn area
    setHasDrawnArea(true);
    setIsDrawingMode(false);

    // Clear regional selection if switching methods
    if (aoiMethod === 'regional') {
      setHasRegionalSelection(false);
      setRegionalAOI(null);
    }

    console.log(`Created ${size} km predefined polygon:`, { bounds, area });
  };

  const handleApplyRegionalSelection = (regions: any[], boundaryLayer: string) => {
    console.log('Applying regional selection:', { regions, boundaryLayer });

    // Calculate combined bounds from regions
    const bounds = calculateRegionalBounds(regions);
    const totalArea = calculateRegionalArea(regions);

    const regionalData = {
      method: 'regional' as const,
      boundaryLayer,
      selectedRegions: regions,
      combinedBounds: bounds,
      totalArea,
      bounds, // For compatibility with existing workflow
      area: totalArea // For compatibility with existing workflow
    };

    setRegionalAOI(regionalData);
    setHasRegionalSelection(true);
    setShowRegionalModal(false);

    console.log('Regional AOI set:', regionalData);
  };

  // Helper functions for regional bounds calculation
  const calculateRegionalBounds = (regions: any[]) => {
    // Simple bounds calculation - can be enhanced
    return {
      north: -22.0, // Mock bounds for South Africa
      south: -35.0,
      east: 33.0,
      west: 16.0
    };
  };

  const calculateRegionalArea = (regions: any[]) => {
    // Mock area calculation - sum of region areas
    return regions.length * 1000; // Mock: 1000 km² per region
  };

  // Interactive boundary filtering handlers
  const handleBoundaryHighlight = (features: GeoJSON.Feature[]) => {
    console.log('🗺️ App: Highlighting', features.length, 'boundary features');
    setHighlightedBoundaries(features);
  };

  const handleBoundaryRegionSelection = (features: GeoJSON.Feature[]) => {
    console.log('🎯 App: Selected', features.length, 'boundary features as AOI');
    
    if (features.length > 0) {
      // Set AOI method to regional and mark as having regional selection
      setAOIMethod('regional');
      setHasRegionalSelection(true);
      setRegionalAOI(features);
      
      // Calculate bounds from features for preview
      let bounds = { north: -Infinity, south: Infinity, east: -Infinity, west: Infinity };
      features.forEach(feature => {
        if (feature.geometry && feature.geometry.type === 'Polygon') {
          const coords = feature.geometry.coordinates[0];
          coords.forEach((coord: any) => {
            const [lng, lat] = coord;
            bounds.north = Math.max(bounds.north, lat);
            bounds.south = Math.min(bounds.south, lat);
            bounds.east = Math.max(bounds.east, lng);
            bounds.west = Math.min(bounds.west, lng);
          });
        } else if (feature.geometry && feature.geometry.type === 'MultiPolygon') {
          // Handle MultiPolygon geometries
          feature.geometry.coordinates.forEach((polygon: any) => {
            polygon[0].forEach((coord: any) => {
              const [lng, lat] = coord;
              bounds.north = Math.max(bounds.north, lat);
              bounds.south = Math.min(bounds.south, lat);
              bounds.east = Math.max(bounds.east, lng);
              bounds.west = Math.min(bounds.west, lng);
            });
          });
        }
      });

      console.log('🔍 App.tsx: Calculated bounds from features:', bounds);
      
      setAoiPreviewData({
        type: 'interactive-boundaries',
        features: features,
        count: features.length,
        bounds: bounds,
        area: calculateRegionalArea(features) // Rough estimation
      });
    }
  };

  useEffect(() => {
    const checkSidebarState = () => {
      const state = localStorage.getItem('sidebarCollapsed') === 'true';
      setSidebarCollapsed(state);
      console.log('Sidebar collapsed state loaded:', state);
    };

    checkSidebarState();
    
    const handleStorageChange = () => {
      checkSidebarState();
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return (
    <div className="app-wrapper">
      <NavBar onNavigate={setCurrentView} onToolsToggle={handleToolsToggle} onHomeReset={resetAllStates} />
      {currentView === 'analytics' ? (
        <Container fluid className="app-container">
          <AnalyticsDashboard />
        </Container>
      ) : (
        <Container fluid className="app-container">
          <div 
            className="main-content" 
            style={{ 
              display: 'flex', 
              height: '100%', 
              width: '100%', 
              minHeight: 'calc(100vh - 60px)',
              position: 'relative'
            }}
          >
            <div
              className={`sidebar-container ${sidebarCollapsed ? 'collapsed' : ''}`}
              style={{
                transition: 'all 0.3s ease',
                height: '100%'
              }}
            >
              <Sidebar
                layers={layers}
                selectedLayerNames={selectedLayers}
                onLayerChange={handleLayerToggle}
                dateRange={dateRange}
                onDateChange={handleDateChange}
                onSearch={handleSearch}
                onPreviewData={handlePreviewData}
                onDownloadData={handleDownloadData}
                onQueryTemporalData={handleQueryTemporalData}
                isLoading={isLoading}
                error={error}
                onDrawModeToggle={handleDrawModeToggle}
                isDrawingMode={isDrawingMode}
                hasDrawnArea={hasDrawnArea}
                onClearDrawnArea={handleClearDrawnArea}
                aoiMethod={aoiMethod}
                onAOIMethodChange={handleAOIMethodChange}
                hasRegionalSelection={hasRegionalSelection}
                onConfigureRegions={handleConfigureRegions}
                onClearRegionalSelection={handleClearRegionalSelection}
                onPredefinedPolygon={handlePredefinedPolygon}
                selectedBasemap={selectedBasemap}
                onBasemapChange={handleBasemapChange}
                // onShowLayerInfo={handleShowLayerInfo}
                onCoordinatePinModeToggle={handleCoordinatePinModeToggle}
                currentCoordinates={currentCoordinates}
                aoiPreviewData={aoiPreviewData}
                onAOIDownload={handleAOIDownload}
                onAOIPreview={handleAOIPreview}
                onBoundaryHighlight={handleBoundaryHighlight}
                onBoundaryRegionSelection={handleBoundaryRegionSelection}
                layerOpacities={layerOpacities}
                onOpacityChange={handleOpacityChange}
              />
            </div>
            <div className="map-container" style={{ flex: 1, width: '100%', height: '100%' }}>
              <MapComponent
                selectedLayerNames={selectedLayers}
                dateRange={dateRange}
                onDrawComplete={setDrawnItems}
                isDrawingMode={isDrawingMode}
                onDrawModeChange={handleDrawModeToggle}
                onAOIComplete={handleAOIComplete}
                sidebarCollapsed={sidebarCollapsed}
                selectedBasemap={selectedBasemap}
                onBasemapChange={handleBasemapChange}
                legendUserMode={legendUserMode}
                onLegendUserModeChange={setLegendUserMode}
                isCoordinatePinMode={coordinatePinMode}
                onCoordinateSelected={handleMapClick}
                onAOIPreview={handleAOIPreview}
                highlightedBoundaries={highlightedBoundaries}
                aoiData={aoiPreviewData}
                layerOpacities={layerOpacities}
                onOpacityChange={handleOpacityChange}
              />
            </div>
          </div>
        </Container>
      )}
      <ToolsPanel isOpen={isToolsPanelOpen} onClose={handleToolsPanelClose} />

      {/* Regional Filter Modal */}
      <RegionalFilterModal
        show={showRegionalModal}
        onHide={() => setShowRegionalModal(false)}
        onApplySelection={handleApplyRegionalSelection}
      />

      {/* Download Modal */}
      <DownloadModal
        show={showDownloadModal}
        onHide={() => setShowDownloadModal(false)}
        selectedLayers={selectedLayers}
        aoiData={aoiPreviewData}
        dateRange={dateRange}
      />
    </div>
  );
}

export default App;
