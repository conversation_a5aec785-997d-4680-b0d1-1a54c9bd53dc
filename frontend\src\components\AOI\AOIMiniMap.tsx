import React, { useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Tile<PERSON>ayer, WMSTileLayer, LayerGroup, Polygon } from 'react-leaflet';
import { LatLngBounds } from 'leaflet';
import { API_CONFIG } from '../../config';
import { convertGeoJSONToWKT } from '../../utils/wktConverter';
import 'leaflet/dist/leaflet.css';

interface LayerWithOpacity {
  name: string;
  opacity?: number;
}

interface AOIMiniMapProps {
  aoiData: {
    bounds: {
      north: number;
      south: number;
      east: number;
      west: number;
    };
    geometry?: any;
    feature?: any;
    name?: string;
  };
  selectedLayers: string[] | LayerWithOpacity[];
  selectedBasemap?: string;
  width?: number;
  height?: number;
  enableZoom?: boolean;
}

const AOIMiniMap: React.FC<AOIMiniMapProps> = ({
  aoiData,
  selectedLayers,
  selectedBasemap = 'osm:osm',
  width = 400,
  height = 200,
  enableZoom = false
}) => {
  const mapRef = useRef<any>(null);

  // Calculate map center and bounds from AOI
  const center: [number, number] = [
    (aoiData.bounds.north + aoiData.bounds.south) / 2,
    (aoiData.bounds.east + aoiData.bounds.west) / 2
  ];

  const bounds = new LatLngBounds(
    [aoiData.bounds.south, aoiData.bounds.west],
    [aoiData.bounds.north, aoiData.bounds.east]
  );

  // Calculate appropriate zoom level and restrictions
  const boundsSize = Math.max(
    aoiData.bounds.north - aoiData.bounds.south,
    aoiData.bounds.east - aoiData.bounds.west
  );

  // Set zoom restrictions based on AOI size
  const minZoom = boundsSize > 5 ? 6 : boundsSize > 1 ? 8 : 10;
  const maxZoom = 16;
  const initialZoom = boundsSize > 5 ? 7 : boundsSize > 1 ? 9 : 11;

  // Fit map to bounds when component mounts and when AOI bounds change
  useEffect(() => {
    if (mapRef.current && aoiData.bounds) {
      const map = mapRef.current;

      const fitToBounds = () => {
        console.log('🎯 Fitting mini map to AOI bounds:', aoiData.bounds);
        console.log('🎯 Mini map center should be:', center);
        console.log('🎯 Mini map zoom should be:', initialZoom);

        // Create bounds from AOI data directly
        const leafletBounds = new LatLngBounds(
          [aoiData.bounds.south, aoiData.bounds.west],
          [aoiData.bounds.north, aoiData.bounds.east]
        );

        console.log('🎯 Leaflet bounds created:', {
          southWest: [aoiData.bounds.south, aoiData.bounds.west],
          northEast: [aoiData.bounds.north, aoiData.bounds.east]
        });

        // Set zoom restrictions first
        map.setMinZoom(minZoom);
        map.setMaxZoom(maxZoom);

        // Force the map to fit exactly to the AOI bounds
        map.fitBounds(leafletBounds, {
          padding: [5, 5],
          maxZoom: initialZoom
        });

        // Verify the result
        setTimeout(() => {
          const currentBounds = map.getBounds();
          const currentCenter = map.getCenter();
          const currentZoom = map.getZoom();

          console.log('🎯 Mini map after fitting:', {
            currentCenter: [currentCenter.lat.toFixed(4), currentCenter.lng.toFixed(4)],
            expectedCenter: center,
            currentZoom,
            expectedZoom: initialZoom,
            currentBounds: {
              north: currentBounds.getNorth().toFixed(4),
              south: currentBounds.getSouth().toFixed(4),
              east: currentBounds.getEast().toFixed(4),
              west: currentBounds.getWest().toFixed(4)
            }
          });
        }, 100);
      };

      // Multiple attempts to ensure proper fitting
      setTimeout(fitToBounds, 100);
      setTimeout(fitToBounds, 500); // Backup attempt
    }
  }, [aoiData.bounds, center, initialZoom, minZoom, maxZoom]);

  // Additional effect to handle dimension changes (e.g., when modal opens)
  // Only trigger on width/height changes, not bounds changes to avoid loops
  useEffect(() => {
    if (mapRef.current && enableZoom) {
      // For modal maps with zoom enabled, ensure proper fitting after render
      const timer = setTimeout(() => {
        const map = mapRef.current;
        if (map) {
          console.log('🔄 Re-fitting modal map after dimension change');
          map.invalidateSize(); // Important for maps in modals
          // Use the current bounds from the map state, not reactive bounds
          const currentBounds = new LatLngBounds(
            [aoiData.bounds.south, aoiData.bounds.west],
            [aoiData.bounds.north, aoiData.bounds.east]
          );
          map.fitBounds(currentBounds, {
            padding: [10, 10],
            maxZoom: Math.min(initialZoom, 12) // Cap the zoom to prevent over-zooming
          });
        }
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [width, height, enableZoom]); // Removed bounds and initialZoom to prevent loops

  console.log('🗺️ AOI Mini Map rendering:', {
    name: aoiData.name || 'Unnamed AOI',
    center,
    bounds: aoiData.bounds,
    boundsSize,
    initialZoom,
    minZoom,
    maxZoom,
    hasGeometry: !!aoiData.geometry,
    layerCount: selectedLayers.length,
    enableZoom,
    dimensions: `${width}x${height}`,
    timestamp: new Date().toISOString()
  });

  return (
    <div style={{ width, height, border: '1px solid #ddd', borderRadius: '4px' }}>
      <MapContainer
        ref={mapRef}
        center={center}
        zoom={initialZoom}
        minZoom={minZoom}
        maxZoom={maxZoom}
        bounds={bounds}
        style={{ width: '100%', height: '100%' }}
        scrollWheelZoom={enableZoom}
        dragging={enableZoom}
        zoomControl={enableZoom}
        doubleClickZoom={enableZoom}
        touchZoom={enableZoom}
        keyboard={enableZoom}
        attributionControl={false}
        boundsOptions={{ padding: [5, 5] }}
      >
        {/* Basemap Layer */}
        {selectedBasemap === 'osm:osm' ? (
          <TileLayer
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            attribution=""
          />
        ) : (
          <WMSTileLayer
            url={`${API_CONFIG.BASE_URL}/ows/wms-proxy`}
            layers={selectedBasemap}
            format="image/jpeg"
            transparent={false}
            version="1.1.1"
            attribution=""
          />
        )}

        {/* Data Layers with Same Logic as Main Map */}
        <LayerGroup>
          {selectedLayers.map((layer, index) => {
            // Handle both string arrays and LayerWithOpacity arrays
            const layerName = typeof layer === 'string' ? layer : layer.name;
            const layerOpacity = typeof layer === 'string' ? 1.0 : (layer.opacity || 1.0);

            console.log(`🗺️ Mini map rendering layer: ${layerName} (opacity: ${layerOpacity})`);

            // Use the same WMS proxy URL as main map
            let layerUrl = `${API_CONFIG.BASE_URL}/ows/wms-proxy`;
            const urlParams = new URLSearchParams();

            // Add temporal parameters if available (same as main map)
            // For mini map, we'll skip temporal for simplicity but keep the structure

            // Add AOI clipping (same logic as main map)
            if (aoiData) {
              // Method 1: CQL filter for precise geometry-based clipping (preferred)
              if (aoiData.geometry && aoiData.feature) {
                try {
                  const geometryWKT = convertGeoJSONToWKT(aoiData.geometry);
                  const geometryField = 'the_geom'; // Default geometry field
                  const cqlFilter = `INTERSECTS(${geometryField}, ${geometryWKT})`;
                  urlParams.set('CQL_FILTER', cqlFilter);
                  console.log(`✂️ Mini map CQL clipping for "${layerName}": ${cqlFilter.substring(0, 100)}...`);
                } catch (error) {
                  console.warn(`❌ Mini map CQL filter failed for "${layerName}":`, error);
                  // Fallback to BBOX clipping
                  if (aoiData.bounds) {
                    const bbox = `${aoiData.bounds.west},${aoiData.bounds.south},${aoiData.bounds.east},${aoiData.bounds.north}`;
                    urlParams.set('BBOX', bbox);
                    urlParams.set('SRS', 'EPSG:4326');
                    console.log(`📦 Mini map BBOX fallback for "${layerName}": ${bbox}`);
                  }
                }
              }
              // Method 2: BBOX clipping for bounds-only AOI
              else if (aoiData.bounds) {
                const bbox = `${aoiData.bounds.west},${aoiData.bounds.south},${aoiData.bounds.east},${aoiData.bounds.north}`;
                urlParams.set('BBOX', bbox);
                urlParams.set('SRS', 'EPSG:4326');
                console.log(`📦 Mini map BBOX clipping for "${layerName}": ${bbox}`);
              }
            }

            if (urlParams.toString()) {
              layerUrl += `?${urlParams.toString()}`;
            }

            // Build effective bounds (same as main map)
            const effectiveBounds = aoiData?.bounds ? [
              [aoiData.bounds.south, aoiData.bounds.west],
              [aoiData.bounds.north, aoiData.bounds.east]
            ] as [[number, number], [number, number]] : undefined;

            // Build AOI signature to force tile refresh (same as main map)
            const aoiSignature = aoiData
              ? (aoiData.feature?.id as any) || (aoiData.bounds ? `${aoiData.bounds.west},${aoiData.bounds.south},${aoiData.bounds.east},${aoiData.bounds.north}` : 'no-aoi')
              : 'no-aoi';

            return (
              <WMSTileLayer
                key={`mini-${layerName}-${aoiSignature}-${index}`} // Force re-render when AOI changes
                url={layerUrl}
                layers={layerName}
                format="image/png"
                transparent={true}
                version="1.1.1"
                bounds={effectiveBounds}
                attribution=""
                opacity={layerOpacity}
                maxZoom={19}
                minZoom={2}
                tileSize={256}
              />
            );
          })}
        </LayerGroup>

        {/* AOI Boundary Outline */}
        {aoiData.geometry && (
          <LayerGroup>
            {(() => {
              const geometry = aoiData.geometry;

              if (geometry.type === 'Polygon') {
                const coordinates = geometry.coordinates[0];
                const positions: [number, number][] = coordinates.map((coord: number[]) => [coord[1], coord[0]]);

                return (
                  <Polygon
                    positions={positions}
                    pathOptions={{
                      fillOpacity: 0,
                      color: '#007bff',
                      weight: 2,
                      dashArray: '5, 5',
                      interactive: false
                    }}
                  />
                );
              } else if (geometry.type === 'MultiPolygon') {
                return (
                  <>
                    {geometry.coordinates.map((polygon: number[][][], index: number) => {
                      const positions: [number, number][] = polygon[0].map((coord: number[]) => [coord[1], coord[0]]);
                      return (
                        <Polygon
                          key={`boundary-${index}`}
                          positions={positions}
                          pathOptions={{
                            fillOpacity: 0,
                            color: '#007bff',
                            weight: 2,
                            dashArray: '5, 5',
                            interactive: false
                          }}
                        />
                      );
                    })}
                  </>
                );
              }

              return null;
            })()}
          </LayerGroup>
        )}
      </MapContainer>
    </div>
  );
};

export default AOIMiniMap;
