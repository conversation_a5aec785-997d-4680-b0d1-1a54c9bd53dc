import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>ge, Spinner, <PERSON><PERSON>, Row, Col, Modal } from 'react-bootstrap';
import { MapPin, Download, Eye, Info, Bug, TestTube } from 'lucide-react';
import { generateAOIScreenshot } from '../../services/geoserverService';

import { debugAOIClipping } from '../../utils/aoiDebugger';
import { testAOIWithAvailableLayers } from '../../utils/layerTester';
import AOIMiniMap from './AOIMiniMap';

interface AOIPreviewCardProps {
  aoiData: {
    type: 'administrative' | 'drawn' | 'pin';
    level?: 'province' | 'district' | 'municipality' | 'ward';
    name?: string;
    code?: string;
    bounds: {
      north: number;
      south: number;
      east: number;
      west: number;
    };
    area: number; // in square kilometers
    coordinates?: any; // For drawn polygons or pin coordinates
    shape?: 'square' | 'circle'; // For pin areas
    dateRange?: {
      startDate: string;
      endDate: string;
    };
    properties?: Record<string, any>; // For storing additional properties, including ward bounds
  };
  selectedLayers: string[];
  selectedBasemap?: string;
  onDownload: (selectedLayers: string[], aoiData: any) => void;
  onShowDetails?: () => void;
}

const AOIPreviewCard: React.FC<AOIPreviewCardProps> = ({
  aoiData,
  selectedLayers,
  selectedBasemap = 'osm:osm',
  onDownload,
  onShowDetails
}) => {
  const [screenshotUrl, setScreenshotUrl] = useState<string | null>(null);
  const [isGeneratingScreenshot, setIsGeneratingScreenshot] = useState(false);
  const [, setScreenshotError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);

  // Function to get layers including administrative boundaries
  const getLayersWithBoundaries = (userSelectedLayers: string[], aoiData: any) => {
    const allLayers: Array<{name: string, opacity?: number}> = [];

    // Add administrative boundary layers based on AOI type and level
    if (aoiData.type === 'administrative' && aoiData.level) {
      console.log(`🗺️ Adding boundary layers for ${aoiData.level} level AOI`);

      // Always add provincial boundaries for context (30% opacity = 70% transparency)
      allLayers.push({
        name: 'geonode:south_africa_provincial_boundaries',
        opacity: 0.3
      });

      // Add district/municipal boundaries if not at province level (30% opacity = 70% transparency)
      if (aoiData.level !== 'province') {
        allLayers.push({
          name: 'geonode:south_africa_municipal_boundaries',
          opacity: 0.3
        });
      }

      // Add ward boundaries if at ward level (30% opacity = 70% transparency)
      if (aoiData.level === 'ward') {
        allLayers.push({
          name: 'geonode:sa_wards2020',
          opacity: 0.3
        });
      }
    }

    // Add user-selected data layers (full opacity)
    userSelectedLayers.forEach(layerName => {
      allLayers.push({
        name: layerName,
        opacity: 1.0
      });
    });

    console.log(`🗺️ Preview layers:`, allLayers);
    return allLayers;
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (screenshotUrl) {
        URL.revokeObjectURL(screenshotUrl);
      }
    };
  }, [screenshotUrl]);

  const generateScreenshot = async () => {
    setIsGeneratingScreenshot(true);
    setScreenshotError(null);

    // Cleanup previous screenshot URL
    if (screenshotUrl) {
      URL.revokeObjectURL(screenshotUrl);
      setScreenshotUrl(null);
    }

    try {
      console.log('Generating AOI preview for:', aoiData);

      // Only generate screenshot if we have data layers selected
      if (selectedLayers.length === 0) {
        throw new Error('Please select at least one data layer to generate preview');
      }

      // Log AOI data details for debugging
      console.log('🖼️ === AOI SCREENSHOT GENERATION DEBUG ===');
      console.log('AOI Data Full:', aoiData);
      console.log('AOI Summary:', {
        type: aoiData.type,
        level: aoiData.level,
        name: aoiData.name,
        boundsPresent: !!aoiData.bounds,
        boundsData: aoiData.bounds,
        area: aoiData.area,
        hasGeometry: !!(aoiData as any).geometry,
        hasFeature: !!(aoiData as any).feature,
        geometryType: (aoiData as any).geometry?.type
      });
      console.log('Selected layers:', selectedLayers);
      console.log('Selected basemap:', selectedBasemap);

      // Get the most accurate bounds available
      let bounds = aoiData.bounds;

      // If we have geometry, calculate bounds from it (most accurate)
      if ((aoiData as any).geometry) {
        console.log('🎯 Calculating bounds from geometry for accurate clipping...');
        try {
          const geometry = (aoiData as any).geometry;
          let allCoords: number[][] = [];

          if (geometry.type === 'Polygon') {
            allCoords = geometry.coordinates[0];
          } else if (geometry.type === 'MultiPolygon') {
            geometry.coordinates.forEach((polygon: any) => {
              polygon.forEach((ring: any) => {
                allCoords.push(...ring);
              });
            });
          }

          if (allCoords.length > 0) {
            let minLng = Infinity, maxLng = -Infinity;
            let minLat = Infinity, maxLat = -Infinity;

            allCoords.forEach((coord: number[]) => {
              const [lng, lat] = coord;
              minLng = Math.min(minLng, lng);
              maxLng = Math.max(maxLng, lng);
              minLat = Math.min(minLat, lat);
              maxLat = Math.max(maxLat, lat);
            });

            bounds = {
              north: maxLat,
              south: minLat,
              east: maxLng,
              west: minLng
            };
            console.log('✅ Calculated bounds from geometry:', bounds);
          }
        } catch (e) {
          console.warn('Failed to calculate bounds from geometry, using AOI bounds:', e);
        }
      }

      // Check if bounds exist directly or in properties (for ward boundaries)
      if (!bounds && aoiData.properties?.bounds) {
        console.log('Using bounds from properties for ward data');
        bounds = aoiData.properties.bounds;
      }

      // Check if bounds exist at all
      if (!bounds) {
        throw new Error('Missing AOI bounds. Please select a valid area of interest.');
      }

      // Ensure all values are numbers
      const north = typeof bounds.north === 'number' ? bounds.north : parseFloat(bounds.north as any);
      const south = typeof bounds.south === 'number' ? bounds.south : parseFloat(bounds.south as any);
      const east = typeof bounds.east === 'number' ? bounds.east : parseFloat(bounds.east as any);
      const west = typeof bounds.west === 'number' ? bounds.west : parseFloat(bounds.west as any);

      // Check for NaN values after parsing
      if (isNaN(north) || isNaN(south) || isNaN(east) || isNaN(west)) {
        console.error('Invalid coordinate values in AOI bounds:', { north, south, east, west });
        throw new Error('Invalid coordinate values in AOI bounds. Please select a different area.');
      }

      // Check for inverted coordinates and correct them
      const correctedBounds = {
        north: Math.max(north, south),
        south: Math.min(north, south),
        east: Math.max(east, west),
        west: Math.min(east, west)
      };

      // Check for out-of-range values and constrain them
      correctedBounds.north = Math.min(90, Math.max(-90, correctedBounds.north));
      correctedBounds.south = Math.min(90, Math.max(-90, correctedBounds.south));
      correctedBounds.east = Math.min(180, Math.max(-180, correctedBounds.east));
      correctedBounds.west = Math.min(180, Math.max(-180, correctedBounds.west));

      // Add a small buffer if bounds are too close or equal
      if (Math.abs(correctedBounds.north - correctedBounds.south) < 0.0001) {
        correctedBounds.north += 0.001;
        correctedBounds.south -= 0.001;
      }

      if (Math.abs(correctedBounds.east - correctedBounds.west) < 0.0001) {
        correctedBounds.east += 0.001;
        correctedBounds.west -= 0.001;
      }

      console.log('Original bounds:', bounds);
      console.log('Corrected bounds:', correctedBounds);
      bounds = correctedBounds;

      // For screenshots, use BBOX clipping instead of complex polygon CQL to avoid HTTP 431 errors
      const dateRange = (aoiData as any)?.dateRange;
      let cqlFilters: string[] | undefined = undefined;

      console.log('🔍 Screenshot generation: Using BBOX clipping for performance...');
      console.log('Has geometry:', !!(aoiData as any).geometry);
      console.log('Geometry type:', (aoiData as any).geometry?.type);

      // For screenshots, we'll rely on BBOX clipping rather than complex CQL
      // This avoids HTTP 431 errors from large WKT strings and improves performance
      console.log('📸 Using BBOX-based clipping for screenshot generation');
      console.log('⚠️ Note: Map layers will still use precise polygon clipping via CQL');

      console.log('📸 Calling generateAOIScreenshot with params:');
      console.log('- Bounds:', bounds);
      console.log('- Layers:', selectedLayers);
      console.log('- Basemap:', selectedBasemap);
      console.log('- CQL Filters:', 0, 'filters (using BBOX for screenshots)');
      console.log('- Date Range:', dateRange);

      const url = await generateAOIScreenshot({
        bounds,
        selectedLayers,
        selectedBasemap,
        dimensions: { width: 400, height: 200 },
        format: 'png',
        cqlFilters,
        dateRange
      });

      console.log('📸 Screenshot URL generated:', !!url);
      if (!url) {
        throw new Error('No preview image was returned. Please check the screenshot service.');
      }
      setScreenshotUrl(url);
    } catch (error) {
      console.error('Failed to generate AOI screenshot:', error);
      setScreenshotError(error instanceof Error ? error.message : 'Failed to generate preview');
    } finally {
      setIsGeneratingScreenshot(false);
    }
  };

  const runDebugTest = async () => {
    console.log('🐛 Running comprehensive AOI debug test...');
    console.log('🐛 AOI Data for debug:', aoiData);

    // Extract region info from AOI data - check multiple possible locations
    const selectedRegions = {
      provinceName: (aoiData as any).provinceName || (aoiData as any).province,
      district: (aoiData as any).district,
      municipality: (aoiData as any).municipality || (aoiData as any).name,
      ward: (aoiData as any).ward
    };

    console.log('🐛 Extracted regions for debug:', selectedRegions);

    const dateRange = (aoiData as any).dateRange;

    try {
      await debugAOIClipping(selectedRegions, selectedLayers, dateRange);
      console.log('🐛 Debug test completed. Check console for detailed results.');
      alert('Debug test completed. Check browser console for detailed results.');
    } catch (error) {
      console.error('🐛 Debug test failed:', error);
      alert('Debug test failed. Check browser console for error details.');
    }
  };

  const testWithWorkingLayers = async () => {
    console.log('🧪 Testing AOI with known working layers...');

    try {
      const testResult = await testAOIWithAvailableLayers(aoiData.bounds);

      if (testResult.success && testResult.url) {
        console.log('✅ Test successful with layers:', testResult.layers);
        setScreenshotUrl(testResult.url);
        alert(`Test successful! Used layers: ${testResult.layers?.join(', ')}`);
      } else {
        console.error('❌ Test failed:', testResult.error);
        alert(`Test failed: ${testResult.error}`);
      }
    } catch (error) {
      console.error('🧪 Test error:', error);
      alert('Test failed. Check console for details.');
    }
  };

  const handleDownload = () => {
    // Include boundary layers in download
    const layersWithBoundaries = getLayersWithBoundaries(selectedLayers, aoiData);
    const layerNames = layersWithBoundaries.map(layer =>
      typeof layer === 'string' ? layer : layer.name
    );

    console.log('🔽 Download requested with layers including boundaries:', layerNames);
    onDownload(layerNames, aoiData);
  };

  const formatArea = (area: number) => {
    // Calculate actual area from geometry if available
    let actualArea = area;

    if ((aoiData as any).geometry && (aoiData as any).feature) {
      try {
        // Use turf.js area calculation if available, otherwise use bounds estimation
        if (typeof window !== 'undefined' && (window as any).turf) {
          const turfArea = (window as any).turf.area((aoiData as any).feature);
          actualArea = turfArea / 1000000; // Convert from m² to km²
        } else if (aoiData.bounds) {
          // Fallback to bounds-based estimation
          const latDiff = aoiData.bounds.north - aoiData.bounds.south;
          const lngDiff = aoiData.bounds.east - aoiData.bounds.west;
          const latKm = latDiff * 111;
          const lngKm = lngDiff * 111 * Math.cos((aoiData.bounds.north + aoiData.bounds.south) / 2 * Math.PI / 180);
          actualArea = Math.abs(latKm * lngKm);
        }
      } catch (error) {
        console.warn('Failed to calculate actual area, using provided value:', error);
      }
    }

    if (actualArea < 1) {
      return `${(actualArea * 100).toFixed(0)} hectares`;
    } else if (actualArea < 1000) {
      return `${actualArea.toFixed(1)} km²`;
    } else {
      return `${(actualArea / 1000).toFixed(1)}k km²`;
    }
  };

  const getLevelIcon = (level?: string) => {
    if (aoiData.type === 'pin') return '📌'; // For pin areas
    if (!level) return '✏️'; // For drawn polygons
    switch (level) {
      case 'province': return '🏛️';
      case 'district': return '🏢';
      case 'municipality': return '🏘️';
      case 'ward': return '🏠';
      default: return '📍';
    }
  };

  const getLevelColor = (level?: string) => {
    if (aoiData.type === 'pin') return 'warning'; // For pin areas
    if (!level) return 'success'; // For drawn polygons
    switch (level) {
      case 'province': return 'primary';
      case 'district': return 'success';
      case 'municipality': return 'warning';
      case 'ward': return 'info';
      default: return 'secondary';
    }
  };

  return (
    <Card className="mb-3" style={{ border: '2px solid #28a745', backgroundColor: '#f8fff9' }}>
      <Card.Header
        style={{
          backgroundColor: '#28a745',
          color: 'white',
          padding: '0.75rem 1rem'
        }}
      >
        <div className="d-flex align-items-center justify-content-between">
          <div className="d-flex align-items-center">
            <span className="me-2">{getLevelIcon(aoiData.level)}</span>
            <h6 className="mb-0">Selected Area of Interest</h6>
          </div>
          <Badge bg="light" text="dark" style={{ fontSize: '0.7rem' }}>
            {aoiData.type === 'drawn' ? 'Drawn Area' :
             aoiData.type === 'pin' ? `Pin Area (${aoiData.shape || 'square'})` :
             aoiData.level ? aoiData.level.charAt(0).toUpperCase() + aoiData.level.slice(1) : 'Custom Area'}
          </Badge>
        </div>
      </Card.Header>

      <Card.Body style={{ padding: '1rem' }}>
        <Row>
          <Col md={7}>
            {/* Area Info */}
            <div className="mb-3">
              <h6 className="text-success mb-2 d-flex align-items-center">
                <MapPin size={16} className="me-2" />
                {aoiData.type === 'drawn' ? 'Custom Drawn Area' :
                 aoiData.type === 'pin' ? (aoiData.name || 'Pin Area of Interest') :
                 aoiData.name || 'Area of Interest'}
              </h6>
              <div className="d-flex gap-2 flex-wrap">
                <Badge bg={getLevelColor(aoiData.level)} style={{ fontSize: '0.75rem' }}>
                  {formatArea(aoiData.area)}
                </Badge>
                {aoiData.code && (
                  <Badge bg="secondary" style={{ fontSize: '0.75rem' }}>
                    {aoiData.code}
                  </Badge>
                )}
                <Badge bg="info" style={{ fontSize: '0.75rem' }}>
                  {selectedLayers.length} layers
                </Badge>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="d-flex gap-2 flex-wrap">
              <Button
                variant="success"
                size="sm"
                onClick={handleDownload}
                disabled={selectedLayers.length === 0}
                className="d-flex align-items-center"
              >
                <Download size={14} className="me-1" />
                Download Data
              </Button>

              {/* Generate Preview button (on demand) */}
              <Button
                variant="outline-primary"
                size="sm"
                onClick={generateScreenshot}
                disabled={isGeneratingScreenshot || selectedLayers.length === 0}
                className="d-flex align-items-center"
              >
                {isGeneratingScreenshot ? (
                  <>
                    <Spinner size="sm" animation="border" className="me-2" />
                    Generating Preview
                  </>
                ) : (
                  <>
                    <Eye size={14} className="me-1" />
                    Generate Preview
                  </>
                )}
              </Button>

              {/* Debug buttons for development */}
              {process.env.NODE_ENV === 'development' && (
                <>
                  <Button
                    variant="outline-warning"
                    size="sm"
                    onClick={runDebugTest}
                    className="d-flex align-items-center"
                  >
                    <Bug size={14} className="me-1" />
                    Debug AOI
                  </Button>

                  <Button
                    variant="outline-success"
                    size="sm"
                    onClick={testWithWorkingLayers}
                    className="d-flex align-items-center"
                  >
                    <TestTube size={14} className="me-1" />
                    Test Working Layers
                  </Button>
                </>
              )}

              {onShowDetails && (
                <Button
	                variant="outline-primary"
	                size="sm"
	                onClick={generateScreenshot}
	                disabled={isGeneratingScreenshot || selectedLayers.length === 0}
	                className="d-flex align-items-center"
	              >
	                {isGeneratingScreenshot ? (
	                  <>
	                    <Spinner size="sm" animation="border" className="me-2" />
	                    Generating Preview
	                  </>
	                ) : (
	                  <>
	                    <Eye size={14} className="me-1" />
	                    Generate Preview
	                  </>
	                )}
	              </Button>
              )}

              {/* Details button */}
              {onShowDetails && (
                <Button
                  variant="outline-info"
                  size="sm"
                  onClick={onShowDetails}
                  className="d-flex align-items-center"
                >
                  <Info size={14} className="me-1" />
                  Details
                </Button>
              )}
            </div>

            {selectedLayers.length === 0 && (
              <Alert variant="warning" className="mt-3 mb-0" style={{ fontSize: '0.85rem' }}>
                💡 Select layers from the Data Layers section below to enable download
              </Alert>
            )}
          </Col>

          <Col md={5}>
            {/* Preview Image */}
            <div className="text-center">
              <div
                style={{
                  width: '100%',
                  height: '120px',
                  backgroundColor: '#f8f9fa',
                  border: '1px solid #dee2e6',
                  borderRadius: '4px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative',
                  overflow: 'hidden'
                }}
              >
                {selectedLayers.length > 0 ? (
                  <div onClick={() => {
                    console.log('🔍 Opening AOI preview modal with data:', aoiData);
                    setShowModal(true);
                  }} style={{ cursor: 'pointer' }} title="Click to view full size">
                    <AOIMiniMap
                      aoiData={aoiData}
                      selectedLayers={getLayersWithBoundaries(selectedLayers, aoiData)}
                      selectedBasemap={selectedBasemap}
                      width={400}
                      height={200}
                    />
                  </div>
                ) : (
                  <div className="text-center p-2">
                    <Eye size={20} className="text-muted mb-1" />
                    <div style={{ fontSize: '0.8rem', color: '#6c757d' }}>
                      Select data layers to see preview
                    </div>
                  </div>
                )}
              </div>

              {/* Mini map updates automatically when layers change */}
            </div>
          </Col>
        </Row>
      </Card.Body>

      {/* AOI Preview Modal */}
      <Modal show={showModal} onHide={() => {
        console.log('🔍 Closing AOI preview modal');
        setShowModal(false);
      }} size="xl" centered>
        <Modal.Header closeButton>
          <Modal.Title>AOI Preview - {aoiData.name || 'Area of Interest'}</Modal.Title>
        </Modal.Header>
        <Modal.Body className="text-center p-0">
          {selectedLayers.length > 0 ? (
            <div style={{ width: '100%', height: '500px' }}>
              <AOIMiniMap
                aoiData={aoiData}
                selectedLayers={getLayersWithBoundaries(selectedLayers, aoiData)}
                selectedBasemap={selectedBasemap}
                width={800}
                height={500}
                enableZoom={true}
              />
            </div>
          ) : (
            <p className="p-4">No layers selected for preview</p>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Close
          </Button>
          <Button variant="primary" onClick={handleDownload}>
            Download Data
          </Button>
        </Modal.Footer>
      </Modal>
    </Card>
  );
};

export default AOIPreviewCard;
