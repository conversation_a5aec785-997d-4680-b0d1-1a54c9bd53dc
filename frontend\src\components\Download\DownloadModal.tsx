import React, { useState, useEffect } from 'react';
import { Modal, Button, Form, Row, Col, Card, Badge, Alert, Spinner } from 'react-bootstrap';
import { Download, FileText, Map, Database, Image, Package } from 'lucide-react';
import { calculateArea } from '../../utils/areaCalculation';
import AOIMiniMap from '../AOI/AOIMiniMap';
import './DownloadModal.css';

interface DownloadOption {
  id: string;
  name: string;
  description: string;
  format: string;
  icon: React.ReactNode;
  category: 'vector' | 'raster' | 'data' | 'document';
  size?: string;
  compatibility: string[];
}

interface DownloadModalProps {
  show: boolean;
  onHide: () => void;
  selectedLayers: string[];
  aoiData: any;
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

const DownloadModal: React.FC<DownloadModalProps> = ({
  show,
  onHide,
  selectedLayers,
  aoiData,
  dateRange
}) => {
  const [selectedFormats, setSelectedFormats] = useState<string[]>([]);
  const [includeMetadata, setIncludeMetadata] = useState(true);
  const [includePreview, setIncludePreview] = useState(true);
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);

  const downloadOptions: DownloadOption[] = [
    // Vector Formats
    {
      id: 'shapefile',
      name: 'Shapefile',
      description: 'Industry standard vector format',
      format: 'SHP',
      icon: <Map size={20} />,
      category: 'vector',
      size: '~2-5 MB',
      compatibility: ['ArcGIS', 'QGIS', 'MapInfo', 'AutoCAD']
    },
    {
      id: 'geojson',
      name: 'GeoJSON',
      description: 'Web-friendly JSON format',
      format: 'JSON',
      icon: <FileText size={20} />,
      category: 'vector',
      size: '~1-3 MB',
      compatibility: ['Web browsers', 'Leaflet', 'OpenLayers', 'QGIS']
    },
    {
      id: 'kml',
      name: 'KML/KMZ',
      description: 'Google Earth compatible format',
      format: 'KML',
      icon: <Map size={20} />,
      category: 'vector',
      size: '~1-2 MB',
      compatibility: ['Google Earth', 'ArcGIS', 'QGIS']
    },
    {
      id: 'geopackage',
      name: 'GeoPackage',
      description: 'Modern SQLite-based format',
      format: 'GPKG',
      icon: <Database size={20} />,
      category: 'vector',
      size: '~2-4 MB',
      compatibility: ['QGIS', 'ArcGIS Pro', 'FME', 'GDAL']
    },
    
    // Raster Formats
    {
      id: 'geotiff',
      name: 'GeoTIFF',
      description: 'Georeferenced raster format',
      format: 'TIF',
      icon: <Image size={20} />,
      category: 'raster',
      size: '~10-50 MB',
      compatibility: ['ArcGIS', 'QGIS', 'ERDAS', 'ENVI']
    },
    {
      id: 'png',
      name: 'PNG with World File',
      description: 'Image with georeferencing',
      format: 'PNG',
      icon: <Image size={20} />,
      category: 'raster',
      size: '~5-20 MB',
      compatibility: ['Most GIS software', 'Web applications']
    },
    
    // Data Formats
    {
      id: 'csv',
      name: 'CSV',
      description: 'Tabular data with coordinates',
      format: 'CSV',
      icon: <FileText size={20} />,
      category: 'data',
      size: '~500 KB - 2 MB',
      compatibility: ['Excel', 'R', 'Python', 'Any spreadsheet software']
    },
    {
      id: 'excel',
      name: 'Excel Workbook',
      description: 'Multi-sheet Excel file',
      format: 'XLSX',
      icon: <FileText size={20} />,
      category: 'data',
      size: '~1-3 MB',
      compatibility: ['Microsoft Excel', 'LibreOffice Calc', 'Google Sheets']
    },
    
    // Document Formats
    {
      id: 'pdf_report',
      name: 'Analysis Report',
      description: 'Comprehensive PDF report',
      format: 'PDF',
      icon: <FileText size={20} />,
      category: 'document',
      size: '~2-10 MB',
      compatibility: ['Any PDF reader']
    },
    {
      id: 'complete_package',
      name: 'Complete Package',
      description: 'All formats in one ZIP file',
      format: 'ZIP',
      icon: <Package size={20} />,
      category: 'document',
      size: '~20-100 MB',
      compatibility: ['All GIS software']
    }
  ];

  // Function to get layers including administrative boundaries
  const getLayersWithBoundaries = (userSelectedLayers: string[], aoiData: any) => {
    const allLayers: Array<{name: string, opacity?: number}> = [];

    // Handle case where aoiData is null (no AOI selected)
    if (!aoiData) {
      console.log('🌍 No AOI data - returning user selected layers only (full extent download)');
      return userSelectedLayers.map(name => ({ name }));
    }

    // Add administrative boundary layers based on AOI type and level
    if (aoiData?.type === 'administrative' && aoiData?.level) {
      console.log(`🗺️ Adding boundary layers for ${aoiData.level} level AOI`);

      // Always add provincial boundaries for context (30% opacity = 70% transparency)
      allLayers.push({
        name: 'geonode:south_africa_provincial_boundaries',
        opacity: 0.3
      });

      // Add district/municipal boundaries if not at province level (30% opacity = 70% transparency)
      if (aoiData?.level !== 'province') {
        allLayers.push({
          name: 'geonode:south_africa_municipal_boundaries',
          opacity: 0.3
        });
      }

      // Add ward boundaries if at ward level (30% opacity = 70% transparency)
      if (aoiData?.level === 'ward') {
        allLayers.push({
          name: 'geonode:sa_wards2020',
          opacity: 0.3
        });
      }
    }

    // Add user-selected data layers (full opacity)
    userSelectedLayers.forEach(layerName => {
      allLayers.push({
        name: layerName,
        opacity: 1.0
      });
    });

    console.log(`🗺️ Download preview layers:`, allLayers);
    return allLayers;
  };

  const handleFormatToggle = (formatId: string) => {
    console.log(`Toggle format: ${formatId}`);
    setSelectedFormats(prev => {
      // Create a copy of the previous formats array
      const currentFormats = [...prev];
      
      // Check if the format is already selected
      const formatIndex = currentFormats.indexOf(formatId);
      
      if (formatIndex !== -1) {
        // Format is already selected, remove it
        console.log(`Removing format: ${formatId}`);
        currentFormats.splice(formatIndex, 1);
      } else {
        // Format is not selected, add it
        console.log(`Adding format: ${formatId}`);
        currentFormats.push(formatId);
      }
      
      console.log(`New selected formats: ${JSON.stringify(currentFormats)}`);
      return currentFormats;
    });
  };
  
  // Debug: Log selected formats when they change
  useEffect(() => {
    console.log(`Selected formats updated: ${JSON.stringify(selectedFormats)}`);
  }, [selectedFormats]);

  const handleSelectAll = (category: string) => {
    const categoryFormats = downloadOptions
      .filter(option => option.category === category)
      .map(option => option.id);
    
    setSelectedFormats(prev => {
      // Remove all formats from this category, then add all
      const otherFormats = prev.filter(id => !categoryFormats.includes(id));
      return Array.from(new Set([...otherFormats, ...categoryFormats]));
    });
  };

  const handleDownload = async () => {
    if (selectedFormats.length === 0) {
      alert('Please select at least one download format.');
      return;
    }

    setIsDownloading(true);
    setDownloadProgress(0);

    // Debug information - log all available data
    console.log('Download initiated with data:', {
      selectedLayers,
      aoiData,
      dateRange,
      selectedFormats,
      includeMetadata,
      includePreview
    });

    // Check for common issues and display warnings
    if (!selectedLayers || selectedLayers.length === 0) {
      console.warn('No layers selected for download');
    }

    if (!aoiData) {
      console.warn('No AOI data available for download - will download full layer extent');
    } else if (!aoiData.bounds && !aoiData.coordinates) {
      console.warn('AOI data missing spatial information (bounds or coordinates) - will download full layer extent');
    }

    try {
      // Download each selected format
      for (let i = 0; i < selectedFormats.length; i++) {
        const formatId = selectedFormats[i];
        setDownloadProgress(Math.round((i / selectedFormats.length) * 100));
        
        // Map formatId to backend format param
        let format = formatId;
        if (formatId === 'shapefile') format = 'shapefile';
        if (formatId === 'geojson') format = 'geojson';
        if (formatId === 'kml') format = 'kml';
        if (formatId === 'csv') format = 'csv';
        if (formatId === 'excel') format = 'excel';
        if (formatId === 'geotiff') format = 'tiff';
        if (formatId === 'png') format = 'png';
        if (formatId === 'pdf_report') format = 'pdf';
        if (formatId === 'geopackage') format = 'geopackage';
        // Add more mappings as needed

        // Build API URL and params for multi-layer download
        const params = new URLSearchParams();
        params.append('format', format);

        // Add layer information - use multi-layer endpoint for multiple layers
        if (selectedLayers && selectedLayers.length > 0) {
          // Always use the multi-layer endpoint for consistency
          params.append('layers', selectedLayers.join(','));
          console.log(`🔽 Using multi-layer download for ${selectedLayers.length} layers:`, selectedLayers);
        } else {
          // Default to flood:flood_areas if no layer selected
          params.append('layers', 'flood:flood_areas');
          console.warn('Using default layer "flood:flood_areas" since no layers were selected');
        }
        
        // AOI clipping - prioritize geometry over bounds for precise clipping
        if (aoiData?.geometry) {
          try {
            const geometryString = JSON.stringify(aoiData.geometry);
            params.append('geometry', geometryString);
            console.log('✂️ Added precise geometry for AOI clipping');
          } catch (error) {
            console.error('❌ Failed to stringify AOI geometry:', error);
          }
        } else if (aoiData?.coordinates) {
          try {
            const geometryString = JSON.stringify({
              type: 'Polygon',
              coordinates: aoiData.coordinates
            });
            params.append('geometry', geometryString);
            console.log('✂️ Added geometry from coordinates for AOI clipping');
          } catch (error) {
            console.error('❌ Failed to stringify coordinates geometry:', error);
          }
        }

        // Add AOI name for better file naming
        if (aoiData?.name) {
          params.append('aoiName', aoiData.name);
          console.log(`📝 Added AOI name: ${aoiData.name}`);
        }

        // Add BBOX for fallback clipping
        if (aoiData?.bounds) {
          const { west, south, east, north } = aoiData.bounds;
          // Use proper BBOX format: minx,miny,maxx,maxy
          params.append('bbox', `${west},${south},${east},${north}`);
          console.log(`Added bbox parameter: ${west},${south},${east},${north}`);
          
          // Some WFS services require an explicit CRS with the bbox
          params.append('srsName', 'EPSG:4326');
        }
        
        // Add date range parameters if available
        if (dateRange?.startDate) {
          params.append('startDate', dateRange.startDate);
          console.log(`Added startDate parameter: ${dateRange.startDate}`);
        }
        
        if (dateRange?.endDate) {
          params.append('endDate', dateRange.endDate);
          console.log(`Added endDate parameter: ${dateRange.endDate}`);
        }
        
        // Add additional options
        if (includeMetadata) {
          params.append('includeMetadata', 'true');
        }
        
        if (includePreview) {
          params.append('includePreview', 'true');
        }
        
        // Add AOI name if available (helps with backend filename generation)
        if (aoiData?.name) {
          params.append('name', aoiData.name);
        }
        
        // Add AOI area if available (useful for backend processing)
        if (aoiData?.area) {
          params.append('area', aoiData.area.toString());
        }

        // Use the multi-layer OWS download endpoint for all formats
        let apiUrl = `/api/ows/multi-layer-download?${params.toString()}`;

        console.log(`🔽 Multi-layer downloading format: ${format} from URL: ${apiUrl}`);

        try {
          // Fetch and trigger download
          console.log(`Initiating download request for format: ${format}`);
          const response = await fetch(apiUrl);
          
          if (!response.ok) {
            console.error(`Download failed for format ${format}: ${response.status} ${response.statusText}`);
            
            // Try to get more detailed error message from response
            let errorDetail = '';
            try {
              const errorData = await response.json();
              errorDetail = errorData.error || errorData.details || errorData.message || '';
              console.error('Server error details:', errorData);
            } catch (e) {
              // If response isn't JSON, just use status text
            }
            
            throw new Error(`Download failed for format ${format}: ${errorDetail || response.statusText}`);
          }
          
          console.log(`Download response received for ${format}, processing...`);
          const blob = await response.blob();
          
          // Check if the blob has content
          if (blob.size === 0) {
            throw new Error(`Empty response received for format ${format}`);
          }
          
          console.log(`Download blob received for ${format}, size: ${blob.size} bytes, type: ${blob.type}`);
          
          // Create download link
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          
          // Set appropriate extension based on format
          let extension;
          switch(format) {
            case 'shapefile': extension = 'zip'; break;
            case 'geojson': extension = 'geojson'; break;
            case 'excel': extension = 'xlsx'; break;
            case 'geopackage': extension = 'gpkg'; break;
            default: extension = format.toLowerCase();
          }
          
          // Create a meaningful filename that includes:
          // 1. Layer name (first selected layer or 'multilayer' if multiple)
          // 2. AOI name if available, otherwise 'aoi'
          // 3. Date range if available
          // 4. Format
          const layerPart = selectedLayers.length > 1 
            ? 'multilayer' 
            : selectedLayers[0]?.split(':').pop() || 'layer';
          
          const aoiPart = aoiData?.name || 'aoi';
          
          let datePart = '';
          if (dateRange?.startDate && dateRange?.endDate) {
            // Format as YYYYMMDD-YYYYMMDD if dates are available
            const startFormatted = dateRange.startDate.replace(/[-:]/g, '').substring(0, 8);
            const endFormatted = dateRange.endDate.replace(/[-:]/g, '').substring(0, 8);
            datePart = `_${startFormatted}-${endFormatted}`;
          }
          
          const filename = `${layerPart}_${aoiPart}${datePart}.${extension}`;
          console.log(`Creating download with filename: ${filename}`);
          link.download = filename;
          
          document.body.appendChild(link);
          link.click();
          link.remove();
          window.URL.revokeObjectURL(url);
          
          console.log(`Download for ${format} completed successfully`);
          
        } catch (error) {
          const downloadError = error as Error;
          console.error(`Error downloading format ${format}:`, downloadError);
          alert(`Failed to download ${format} format: ${downloadError.message || 'Unknown error'}`);
          // Continue with other formats
        }
        
        // Update progress after each format
        setDownloadProgress(Math.round(((i + 1) / selectedFormats.length) * 100));
      }
      
      // Only close if at least one download was successful
      onHide(); 
    } catch (err) {
      const error = err as Error;
      console.error('Download failed:', error);
      alert(`Download failed: ${error.message || 'Unknown error'}`);
    } finally {
      setIsDownloading(false);
      setDownloadProgress(0);
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'vector': return <Map size={16} />;
      case 'raster': return <Image size={16} />;
      case 'data': return <Database size={16} />;
      case 'document': return <FileText size={16} />;
      default: return <Download size={16} />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'vector': return 'primary';
      case 'raster': return 'success';
      case 'data': return 'warning';
      case 'document': return 'info';
      default: return 'secondary';
    }
  };

  const groupedOptions = downloadOptions.reduce((acc, option) => {
    if (!acc[option.category]) {
      acc[option.category] = [];
    }
    acc[option.category].push(option);
    return acc;
  }, {} as Record<string, DownloadOption[]>);

  return (
    <Modal 
      show={show} 
      onHide={onHide} 
      size="xl" 
      centered
      style={{ zIndex: 9999 }} // Ensure modal appears above all other elements
    >
      <Modal.Header style={{ backgroundColor: '#007bff', color: 'white' }}>
        <Modal.Title>
          <Download size={24} className="me-2" />
          Download Area of Interest Data
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body>
        {/* Download Summary */}
        <Alert variant={aoiData ? "info" : "warning"} className="mb-4">
          <strong>Download Summary:</strong><br />
          • Selected Layers: {selectedLayers.length} layers ({selectedLayers.join(', ')})<br />
          {/* Calculate area if missing but bounds are available */}
          • Area: {aoiData?.area ? `${aoiData.area.toFixed(2)} km²` :
                  aoiData?.bounds ? `${calculateArea(aoiData.bounds).toFixed(2)} km²` :
                  aoiData?.coordinates ? `${calculateArea(aoiData.coordinates).toFixed(2)} km²` :
                  aoiData ? 'Not specified' : 'Full layer extent (no AOI selected)'}<br />
          • Date Range: {dateRange.startDate || 'Not specified'} to {dateRange.endDate || 'Not specified'}<br />
          • AOI Method: {aoiData?.method || aoiData?.type || (aoiData ? 'drawn' : 'No AOI - full extent download')}
          {!aoiData && (
            <>
              <br />
              <strong>⚠️ Note:</strong> No Area of Interest selected. Download will include full layer extent.
            </>
          )}
        </Alert>
        
        {/* AOI Preview Map */}
        {includePreview && selectedLayers.length > 0 && (
          <Card className="mt-3 mb-4">
            <Card.Header>
              {aoiData ? 'Area of Interest Preview' : 'Layer Preview (Full Extent)'}
            </Card.Header>
            <Card.Body className="text-center">
              {aoiData ? (
                <div style={{ width: '100%', height: '300px' }}>
                  <AOIMiniMap
                    aoiData={aoiData}
                    selectedLayers={getLayersWithBoundaries(selectedLayers, aoiData)}
                    width={600}
                    height={300}
                    enableZoom={true}
                  />
                </div>
              ) : (
                <div className="text-center p-4">
                  <div className="text-muted mb-3">
                    <Map size={48} className="mx-auto d-block mb-2" />
                    <h6>No Area of Interest Selected</h6>
                    <p className="small">
                      Download will include full extent of selected layers.<br/>
                      Select an AOI first to preview a specific area.
                    </p>
                  </div>
                </div>
              )}
              <p className="mt-2 small text-muted">
                {aoiData
                  ? `Preview of selected area with ${selectedLayers.length} layer${selectedLayers.length !== 1 ? 's' : ''}`
                  : `Full extent download with ${selectedLayers.length} layer${selectedLayers.length !== 1 ? 's' : ''}`
                }
              </p>
            </Card.Body>
          </Card>
        )}

        {/* Format Selection */}
        <h6 className="text-primary mb-3">Select Download Formats:</h6>
        
        {Object.entries(groupedOptions).map(([category, options]) => (
          <div key={category} className="mb-4">
            <div className="d-flex align-items-center justify-content-between mb-2">
              <h6 className="mb-0">
                <Badge bg={getCategoryColor(category)} className="me-2">
                  {getCategoryIcon(category)}
                </Badge>
                {category.charAt(0).toUpperCase() + category.slice(1)} Formats
              </h6>
              <Button
                variant="outline-secondary"
                size="sm"
                onClick={() => handleSelectAll(category)}
              >
                Select All
              </Button>
            </div>
            
            <Row>
              {options.map(option => (
                <Col md={6} lg={4} key={option.id} className="mb-3">
                  <Card 
                    className={`h-100 format-card ${selectedFormats.includes(option.id) ? 'selected' : ''}`}
                    onClick={(e) => {
                      // Only toggle if the click wasn't on the checkbox
                      if (!(e.target as HTMLElement).closest('.form-check')) {
                        handleFormatToggle(option.id);
                      }
                    }}
                  >
                    <Card.Body className="p-3">
                      <Form.Check
                        type="checkbox"
                        id={`format-check-${option.id}`}
                        checked={selectedFormats.includes(option.id)}
                        onChange={(e) => {
                          e.stopPropagation(); // Stop event propagation
                          handleFormatToggle(option.id);
                        }}
                        className="mb-2"
                        label=""
                      />
                      <div className="d-flex align-items-center mb-2">
                        {option.icon}
                        <strong className="ms-2">{option.name}</strong>
                        <Badge bg="secondary" className="ms-auto">{option.format}</Badge>
                      </div>
                      <p className="text-muted small mb-2">{option.description}</p>
                      {option.size && (
                        <small className="text-success">Est. size: {option.size}</small>
                      )}
                      <div className="mt-2">
                        <small className="text-muted">
                          Compatible with: {option.compatibility.slice(0, 2).join(', ')}
                          {option.compatibility.length > 2 && ` +${option.compatibility.length - 2} more`}
                        </small>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              ))}
            </Row>
          </div>
        ))}

        {/* Additional Options */}
        <h6 className="text-primary mb-3">Additional Options:</h6>
        <Row>
          <Col md={6}>
            <Form.Check
              type="checkbox"
              checked={includeMetadata}
              onChange={(e) => setIncludeMetadata(e.target.checked)}
              label="Include metadata files"
              className="mb-2"
            />
            <small className="text-muted">Includes projection files, data dictionaries, and source information</small>
          </Col>
          <Col md={6}>
            <Form.Check
              type="checkbox"
              checked={includePreview}
              onChange={(e) => setIncludePreview(e.target.checked)}
              label="Include preview images"
              className="mb-2"
            />
            <small className="text-muted">Includes PNG thumbnails and overview maps</small>
          </Col>
        </Row>

        {/* Download Progress */}
        {isDownloading && (
          <div className="mt-4">
            <div className="d-flex align-items-center mb-2">
              <Spinner animation="border" size="sm" className="me-2" />
              <span>Preparing download... {downloadProgress}%</span>
            </div>
            <div className="progress">
              <div 
                className="progress-bar" 
                style={{ width: `${downloadProgress}%` }}
              ></div>
            </div>
          </div>
        )}
      </Modal.Body>
      
      <Modal.Footer>
        <div className="d-flex justify-content-between w-100">
          <div>
            <small className="text-muted">
              {selectedFormats.length} format{selectedFormats.length !== 1 ? 's' : ''} selected
            </small>
          </div>
          <div>
            <Button variant="secondary" onClick={onHide} disabled={isDownloading}>
              Cancel
            </Button>
            <Button 
              variant="primary" 
              onClick={handleDownload}
              disabled={selectedFormats.length === 0 || isDownloading}
              className="ms-2"
            >
              {isDownloading ? (
                <>
                  <Spinner animation="border" size="sm" className="me-2" />
                  Downloading...
                </>
              ) : (
                <>
                  <Download size={16} className="me-2" />
                  Download Selected
                </>
              )}
            </Button>
          </div>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default DownloadModal;
