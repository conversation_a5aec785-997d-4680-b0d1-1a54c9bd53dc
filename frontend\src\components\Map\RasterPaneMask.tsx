import React, { useEffect, useMemo } from 'react';
import { useMap } from 'react-leaflet';

type Props = {
  geometry?: GeoJSON.Geometry | null;
  paneName?: string; // default 'rasterOverlayPane'
  enabled?: boolean;
};

const RasterPaneMask: React.FC<Props> = ({ geometry, paneName = 'rasterOverlayPane', enabled = true }) => {
  const map = useMap();

  // Only apply for Polygon/MultiPolygon
  const geomType = geometry?.type;
  const isPolygonal = geomType === 'Polygon' || geomType === 'MultiPolygon';

  // Ensure the custom pane exists
  useEffect(() => {
    if (!map || !enabled) return;
    const panes = (map as any)._panes;
    if (!panes[paneName]) {
      map.createPane(paneName);
      // Keep this above basemap but around overlay z-index
      map.getPane(paneName)!.style.zIndex = '420';
      console.log(`🎭 Created raster pane: ${paneName} with z-index 420`);
    }
  }, [map, paneName, enabled]);

  useEffect(() => {
    if (!map) return;

    const rasterPane = map.getPane(paneName);
    if (!enabled || !rasterPane || !isPolygonal || !geometry) {
      // Remove any existing mask when disabled or geometry invalid
      if (rasterPane) {
        rasterPane.style.mask = '';
        (rasterPane.style as any).webkitMask = '';
        console.log('🧹 Cleared raster pane mask');
      }
      const old = document.getElementById('aoi-pane-mask-svg');
      if (old?.parentElement) {
        old.parentElement.removeChild(old);
        console.log('🗑️ Removed SVG mask element');
      }
      return;
    }

    console.log(`🎭 Applying SVG mask to ${paneName} for ${geomType} geometry`);

    const container = map.getContainer();
    const mapSize = map.getSize();

    // Create or reuse hidden SVG with mask defs
    let svg = document.getElementById('aoi-pane-mask-svg') as SVGSVGElement | null;
    if (!svg) {
      svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      svg.setAttribute('id', 'aoi-pane-mask-svg');
      svg.setAttribute('width', String(mapSize.x));
      svg.setAttribute('height', String(mapSize.y));
      svg.style.position = 'absolute';
      svg.style.top = '0';
      svg.style.left = '0';
      svg.style.pointerEvents = 'none';
      svg.style.overflow = 'visible';
      svg.style.zIndex = '1000';
      // keep it in the map container so url(#...) resolves
      container.appendChild(svg);
      console.log('✨ Created SVG mask element');
    } else {
      svg.setAttribute('width', String(mapSize.x));
      svg.setAttribute('height', String(mapSize.y));
    }

    const buildMask = () => {
      if (!svg) return;
      
      // Clear svg
      while (svg.firstChild) svg.removeChild(svg.firstChild);

      const currentMapSize = map.getSize();
      svg.setAttribute('width', String(currentMapSize.x));
      svg.setAttribute('height', String(currentMapSize.y));

      const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
      const mask = document.createElementNS('http://www.w3.org/2000/svg', 'mask');
      mask.setAttribute('id', 'aoiPaneMask');
      mask.setAttribute('maskUnits', 'userSpaceOnUse');
      mask.setAttribute('x', '0');
      mask.setAttribute('y', '0');
      mask.setAttribute('width', String(currentMapSize.x));
      mask.setAttribute('height', String(currentMapSize.y));

      // Start with a black rect (hide everything)
      const bg = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      bg.setAttribute('x', '0');
      bg.setAttribute('y', '0');
      bg.setAttribute('width', String(currentMapSize.x));
      bg.setAttribute('height', String(currentMapSize.y));
      bg.setAttribute('fill', 'black');
      mask.appendChild(bg);

      const addWhitePolygon = (points: string) => {
        const poly = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
        poly.setAttribute('points', points);
        poly.setAttribute('fill', 'white'); // show raster inside AOI
        mask.appendChild(poly);
      };

      const addBlackPolygon = (points: string) => {
        const poly = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
        poly.setAttribute('points', points);
        poly.setAttribute('fill', 'black'); // hide inner holes
        mask.appendChild(poly);
      };

      const toPointList = (coords: number[][]) => {
        // coords: [ [lng, lat], ... ]
        try {
          const pts = coords.map(([lng, lat]) => {
            const p = map.latLngToContainerPoint([lat, lng]);
            return `${p.x},${p.y}`;
          });
          return pts.join(' ');
        } catch (error) {
          console.warn('Error converting coordinates to points:', error);
          return '';
        }
      };

      // Build mask from geometry (white = visible area, black = hidden)
      try {
        if (geomType === 'Polygon') {
          const polygon = geometry as GeoJSON.Polygon;
          const rings = polygon.coordinates || [];
          if (rings[0]) {
            const outerPoints = toPointList(rings[0]);
            if (outerPoints) addWhitePolygon(outerPoints); // outer
          }
          // holes
          for (let i = 1; i < rings.length; i++) {
            const holePoints = toPointList(rings[i]);
            if (holePoints) addBlackPolygon(holePoints);
          }
        } else if (geomType === 'MultiPolygon') {
          const mpoly = geometry as GeoJSON.MultiPolygon;
          for (const poly of mpoly.coordinates) {
            if (poly[0]) {
              const outerPoints = toPointList(poly[0]);
              if (outerPoints) addWhitePolygon(outerPoints); // outer
            }
            for (let i = 1; i < poly.length; i++) {
              const holePoints = toPointList(poly[i]);
              if (holePoints) addBlackPolygon(holePoints); // holes
            }
          }
        }

        defs.appendChild(mask);
        svg.appendChild(defs);

        // Apply mask to raster pane
        // Use both standard and webkit for Safari
        (rasterPane.style as any).mask = 'url(#aoiPaneMask)';
        (rasterPane.style as any).webkitMask = 'url(#aoiPaneMask)';
        
        console.log('✅ Applied SVG mask to raster pane');
      } catch (error) {
        console.error('Error building SVG mask:', error);
      }
    };

    // Build initial mask
    buildMask();

    // Update mask on map events
    const onMove = () => {
      setTimeout(buildMask, 50); // Small delay for performance
    };
    const onZoom = () => {
      setTimeout(buildMask, 50);
    };
    const onResize = () => {
      setTimeout(buildMask, 100);
    };

    map.on('move', onMove);
    map.on('zoom', onZoom);
    map.on('resize', onResize);
    map.on('moveend', onMove);
    map.on('zoomend', onZoom);

    return () => {
      map.off('move', onMove);
      map.off('zoom', onZoom);
      map.off('resize', onResize);
      map.off('moveend', onMove);
      map.off('zoomend', onZoom);
      // SVG will be cleaned up in next run or on disable
    };
  }, [map, geometry, enabled, paneName, isPolygonal, geomType]);

  // Expose debugging function
  useEffect(() => {
    (window as any).debugRasterPaneMask = () => {
      console.log('🔍 RasterPaneMask Debug Info:');
      console.log('Geometry:', geometry);
      console.log('Geometry Type:', geomType);
      console.log('Is Polygonal:', isPolygonal);
      console.log('Enabled:', enabled);
      console.log('Pane Name:', paneName);
      
      const rasterPane = map?.getPane(paneName);
      console.log('Raster Pane:', rasterPane);
      console.log('Pane Mask Style:', rasterPane?.style.mask);
      
      const svg = document.getElementById('aoi-pane-mask-svg');
      console.log('SVG Element:', svg);
      console.log('SVG Connected:', svg?.isConnected);
    };

    return () => {
      delete (window as any).debugRasterPaneMask;
    };
  }, [geometry, geomType, isPolygonal, enabled, paneName, map]);

  return null;
};

export default RasterPaneMask;
