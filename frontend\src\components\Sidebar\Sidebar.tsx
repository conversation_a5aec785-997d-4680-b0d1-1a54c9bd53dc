import { useState, useEffect, useCallback, useMemo } from 'react';
import { Form } from 'react-bootstrap';
import RegionSelector from './RegionSelector';
import DataLayers from './DataLayers';
import DataActions from './DataActions';
import AOIPreviewCard from '../AOI/AOIPreviewCard';
import { ChevronLeft, ChevronRight, Trash2 } from 'lucide-react';
import './Sidebar.css';
import { LayerDiscovery } from '../../types/discovery';
import {
  loadProvinces,
  loadMunicipalities,
  loadDistricts,
  getFilteredBoundaryFeatures,
  getSelectedBoundaryGeometry,
  AdministrativeRegion,
  BoundaryFilters
} from '../../services/unifiedBoundaryService';



interface SidebarProps {
  layers: LayerDiscovery[];
  selectedLayerNames: string[];
  onLayerChange: (layerName: string) => void;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  onDateChange: (type: 'startDate' | 'endDate', value: string) => void;
  onSearch: (query: string) => void;
  onPreviewData: () => void;
  onDownloadData: () => void;
  onQueryTemporalData?: () => void;
  isLoading?: boolean;
  error?: string | null;
  // AOI functionality
  onDrawModeToggle: (isDrawing: boolean) => void;
  isDrawingMode: boolean;
  hasDrawnArea: boolean;
  onClearDrawnArea: () => void;
  // Regional AOI functionality
  aoiMethod: 'drawn' | 'regional';
  onAOIMethodChange: (method: 'drawn' | 'regional') => void;
  hasRegionalSelection: boolean;
  onConfigureRegions: () => void;
  onClearRegionalSelection: () => void;
  // Predefined polygon functionality
  onPredefinedPolygon: (size: string) => void;
  // Basemap functionality
  selectedBasemap?: string;
  onBasemapChange?: (basemapName: string) => void;
  // Coordinate functionality
  onCoordinatePinModeToggle?: (enabled: boolean) => void;
  currentCoordinates?: string;
  // AOI Preview functionality
  aoiPreviewData?: any;
  onAOIDownload?: (selectedLayers: string[], aoiData: any) => void;
  onAOIPreview?: (aoiData: any) => void;
  // Interactive boundary filtering
  onBoundaryHighlight?: (features: GeoJSON.Feature[]) => void;
  onBoundaryRegionSelection?: (features: GeoJSON.Feature[]) => void;
  // Layer opacity control
  layerOpacities?: { [layerName: string]: number };
  onOpacityChange?: (layerName: string, opacity: number) => void;
}

function Sidebar(props: SidebarProps) {
  // State for sidebar collapse
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Gate preview rendering until user clicks Preview
  const [previewRequested, setPreviewRequested] = useState(false);

  // State for region of interest toggle
  const [selectRegionOfInterest, setSelectRegionOfInterest] = useState(false);

  // State for nested section toggles
  const [nestedSections, setNestedSections] = useState({
    dateRange: false,      // Collapsed by default
    drawingTools: false    // Collapsed by default
  });

  // State for coordinate input
  const [isPinningMode, setIsPinningMode] = useState(false);

  // Local AOI preview state for sidebar display
  const [localAoiPreviewData, setLocalAoiPreviewData] = useState<any>(null);

  // State for administrative boundaries - updated to use unified types
  const [administrativeBoundaries, setAdministrativeBoundaries] = useState({
    provinces: [] as AdministrativeRegion[],
    municipalities: [] as AdministrativeRegion[],
    districts: [] as AdministrativeRegion[]
  });

  // State for selected administrative regions (with codes)
  const [selectedRegions, setSelectedRegions] = useState({
    province: '', // province id
    provinceName: '', // province name
    municipality: '',
    municipalityCode: '',
    district: '',
    districtCode: ''
  });

  // Loading states for administrative boundaries
  const [boundaryLoading, setBoundaryLoading] = useState({
    provinces: false,
    municipalities: false,
    districts: false
  });

  // Enhanced loading states for AOI operations
  const [aoiLoading, setAoiLoading] = useState({
    highlighting: false,
    zooming: false,
    progress: 0
  });

  const toggleNestedSection = (section: keyof typeof nestedSections) => {
    const newState = !nestedSections[section];

    setNestedSections(prev => ({
      ...prev,
      [section]: newState
    }));

    // If toggling coordinates section, handle pin mode
    if (section === 'coordinates') {
      // When closing coordinates section, also disable pin mode
      if (!newState && isPinningMode) {
        setIsPinningMode(false);
        if (props.onCoordinatePinModeToggle) {
          props.onCoordinatePinModeToggle(false);
        }
      }
    }
  };

  // Use props directly instead of destructuring

  // Handle external AOI preview data (from drawn polygons)
  useEffect(() => {
    if (props.aoiPreviewData) {
      // Clear local AOI preview when external data is provided
      setLocalAoiPreviewData(null);
    }
  }, [props.aoiPreviewData]);

  // Clear all AOI states when "Select region of interest" toggle is turned off
  useEffect(() => {
    if (!selectRegionOfInterest) {
      console.log('🧹 Clearing all AOI states - region of interest toggle disabled');

      // Clear selected regions
      setSelectedRegions({
        province: '',
        provinceName: '',
        municipality: '',
        municipalityCode: '',
        district: '',
        districtCode: ''
      });

      // Clear AOI preview data
      setLocalAoiPreviewData(null);

      // Clear highlighted boundaries if prop is available
      if (props.onBoundaryHighlight) {
        props.onBoundaryHighlight([]);
      }
    }
  }, [selectRegionOfInterest]); // Removed props.onBoundaryHighlight to prevent circular dependencies

  // Clear administrative boundaries when pin mode is active
  useEffect(() => {
    if (isPinningMode) {
      // Clear all administrative selections when entering pin mode
      setSelectedRegions({
        province: '',
        provinceName: '',
        municipality: '',
        municipalityCode: '',
        district: '',
        districtCode: ''
      });

      // Clear local AOI preview
      setLocalAoiPreviewData(null);

      console.log('Pin mode activated - cleared administrative boundaries');
    }
  }, [isPinningMode]);

  // Load provinces on component mount
  useEffect(() => {
    const loadProvincesData = async () => {
      setBoundaryLoading(prev => ({ ...prev, provinces: true }));
      try {
        const provinces = await loadProvinces();
        setAdministrativeBoundaries(prev => ({ ...prev, provinces }));
      } catch (error) {
        console.error('Failed to load provinces:', error);
      } finally {
        setBoundaryLoading(prev => ({ ...prev, provinces: false }));
      }
    };

    loadProvincesData();
  }, []);

  // Load municipalities when province changes
  useEffect(() => {
    if (selectedRegions.province) {
      const loadMunicipalitiesData = async () => {
        setBoundaryLoading(prev => ({ ...prev, municipalities: true }));
        try {
          const municipalities = await loadMunicipalities(selectedRegions.province);
          setAdministrativeBoundaries(prev => ({ ...prev, municipalities }));
          // Clear dependent selections
          setSelectedRegions(prev => ({
            ...prev,
            municipality: '',
            municipalityCode: '',
            district: '',
            districtCode: ''
          }));
        } catch (error) {
          console.error('Failed to load municipalities:', error);
        } finally {
          setBoundaryLoading(prev => ({ ...prev, municipalities: false }));
        }
      };

      loadMunicipalitiesData();
    } else {
      // Clear municipalities if no province selected
      setAdministrativeBoundaries(prev => ({ ...prev, municipalities: [], districts: [] }));
      setSelectedRegions(prev => ({
        ...prev,
        municipality: '',
        municipalityCode: '',
        district: '',
        districtCode: ''
      }));
    }
  }, [selectedRegions.province]);

  // Load districts when province changes (if available)
  useEffect(() => {
    if (selectedRegions.province) {
      const loadDistrictsData = async () => {
        setBoundaryLoading(prev => ({ ...prev, districts: true }));
        try {
          const districts = await loadDistricts(selectedRegions.province);
          setAdministrativeBoundaries(prev => ({ ...prev, districts }));
        } catch (error) {
          console.error('Failed to load districts:', error);
        } finally {
          setBoundaryLoading(prev => ({ ...prev, districts: false }));
        }
      };

      loadDistrictsData();
    }
  }, [selectedRegions.province]);

  // Load municipalities for district (handles metros and non-metros)
  useEffect(() => {
    // Prevent execution if district or province is empty to avoid infinite loops
    if (!selectedRegions.district || !selectedRegions.province) {
      return;
    }

    const selectedDistrictInfo = administrativeBoundaries.districts.find(
      (d: AdministrativeRegion) => d.name === selectedRegions.district
    );

    // Prevent execution if districts haven't loaded yet
    if (administrativeBoundaries.districts.length === 0) {
      return;
    }

    const loadMunicipalitiesForDistrict = async () => {
      setBoundaryLoading(prev => ({ ...prev, municipalities: true }));
      try {
        if (selectedDistrictInfo?.properties?.isMetro) {
          // Metro areas have no local municipalities
          console.log(`District ${selectedRegions.district} is a metropolitan municipality - no local municipalities`);

          // For metros, clear municipalities since they don't have local municipalities
          setAdministrativeBoundaries(prev => ({ ...prev, municipalities: [] }));
        } else {
          // Load local municipalities for this district
          const municipalities = await loadMunicipalities(selectedRegions.province, selectedRegions.district);

          setAdministrativeBoundaries(prev => ({ ...prev, municipalities }));
          // Clear dependent selections
          setSelectedRegions(prev => ({
            ...prev,
            municipality: '',
            municipalityCode: ''
          }));
        }
      } catch (error) {
        console.error('Failed to load municipalities for district:', error);
      } finally {
        setBoundaryLoading(prev => ({ ...prev, municipalities: false }));
      }
    };

    loadMunicipalitiesForDistrict();
  }, [selectedRegions.district, selectedRegions.province]); // Removed administrativeBoundaries.districts to break circular dependency



  // Handler for administrative region selection with auto-trigger functionality
  const handleRegionChange = (level: keyof typeof selectedRegions, value: string) => {
    console.log(`Changing ${level} to:`, value);
    const updates: any = { [level]: value };

    if (level === 'province') {
      // Find province name by id
      const selectedProvince = administrativeBoundaries.provinces.find(p => p.id === value);
      updates.provinceName = selectedProvince ? selectedProvince.name : '';
      // Clear all dependent selections
      updates.district = '';
      updates.districtCode = '';
      updates.municipality = '';
      updates.municipalityCode = '';
    }

    // When district changes, also store the district code
    if (level === 'district') {
      const selectedDistrictInfo = administrativeBoundaries.districts.find(
        d => d.name === value
      );
      updates.districtCode = selectedDistrictInfo?.id || '';
      console.log("Selected district info:", selectedDistrictInfo);

      // Clear dependent selections
      updates.municipality = '';
      updates.municipalityCode = '';
    }

    // When municipality changes, also store the municipality code
    if (level === 'municipality') {
      // The dropdown is now using the municipality name as value
      updates.municipality = value;

      // Find and store the code for API calls
      const selectedMunicipalityInfo = administrativeBoundaries.municipalities.find(
        m => m.name === value
      );
      updates.municipalityCode = selectedMunicipalityInfo?.id || '';
      console.log("Selected municipality:", selectedMunicipalityInfo);

    }

    setSelectedRegions(prev => ({
      ...prev,
      ...updates
    }));
  };

  // Helper functions to determine completion states for sequential workflow - memoized to prevent re-renders
  const isAdministrativeSelectionComplete = useCallback(() => {
    // Must have at least province
    if (!selectedRegions.provinceName) return false;

    // If no district selected, just province is enough for completion
    if (!selectedRegions.district) return true;

    // Check if district is metro
    const isMetro = administrativeBoundaries.districts.find(d =>
      d.name === selectedRegions.district && d.properties?.isMetro
    );

    if (isMetro) {
      // For metro districts, district selection is complete
      return true;
    }

    // For non-metro districts, check municipality selection
    if (selectedRegions.municipality) {
      // Municipality selection is complete
      return true;
    }

    // If no municipalities available for district, district selection is complete
    if (administrativeBoundaries.municipalities.length === 0 && !boundaryLoading.municipalities) return true;

    // Otherwise, need further selection
    return false;
  }, [
    selectedRegions.provinceName,
    selectedRegions.district,
    selectedRegions.municipality,
    administrativeBoundaries.districts,
    administrativeBoundaries.municipalities,
    boundaryLoading.municipalities
  ]);

  const isDateRangeComplete = useCallback(() => {
    // Date range is complete if we have at least province and both dates
    return selectedRegions.provinceName &&
           props.dateRange.startDate &&
           props.dateRange.endDate;
  }, [selectedRegions.provinceName, props.dateRange.startDate, props.dateRange.endDate]);

  const isLayerSelectionComplete = useCallback(() => {
    return isDateRangeComplete() && props.selectedLayerNames.length > 0;
  }, [isDateRangeComplete, props.selectedLayerNames.length]);

  // Auto-trigger map rendering and AOI preview when regions change
  const triggerBoundaryUpdates = useCallback(async () => {
    // Only trigger if we have at least a province selected
    if (!selectedRegions.provinceName) {
      // Clear map rendering and AOI preview
      if (props.onBoundaryHighlight) {
        props.onBoundaryHighlight([]);
      }
      setLocalAoiPreviewData(null);
      setAoiLoading({ highlighting: false, zooming: false, progress: 0 });
      return;
    }

    try {
      // Start loading process
      setAoiLoading({ highlighting: true, zooming: false, progress: 10 });
      console.log('🔄 Starting AOI boundary loading process...');
      console.log('🔍 DEBUG: Current selectedRegions state:', selectedRegions);
      console.log('🔍 DEBUG: Props selectedRegions:', {
        provinceName: props.selectedRegions?.provinceName,
        district: props.selectedRegions?.district,
        municipality: props.selectedRegions?.municipality
      });

      // Build filters based on current selections
      const filters: BoundaryFilters = {};

      if (selectedRegions.provinceName) {
        filters.province = selectedRegions.provinceName;
      }
      if (selectedRegions.district) {
        filters.district = selectedRegions.district;
      }
      if (selectedRegions.municipality) {
        filters.municipality = selectedRegions.municipality;
      }

      console.log('🎯 Triggering boundary updates with filters:', filters);
      console.log('🔍 DEBUG: Selected regions state:', {
        provinceName: selectedRegions.provinceName,
        district: selectedRegions.district,
        municipality: selectedRegions.municipality,
        districtCode: selectedRegions.districtCode,
        municipalityCode: selectedRegions.municipalityCode
      });

      // Update progress - fetching boundaries
      setAoiLoading(prev => ({ ...prev, progress: 30 }));

      // Get filtered boundary features for map rendering
      const boundaryResult = await getFilteredBoundaryFeatures(filters);

      // Update progress - boundaries loaded
      setAoiLoading(prev => ({ ...prev, progress: 60 }));

      console.log('🔍 DEBUG: Boundary result:', {
        featureCount: boundaryResult.features.length,
        totalCount: boundaryResult.totalCount,
        isPartial: boundaryResult.isPartial,
        firstFeatureProps: boundaryResult.features[0]?.properties || 'No features'
      });

      if (boundaryResult.features.length > 0) {
        // Force clear existing boundaries first, then set new ones (ensures proper refresh)
        if (props.onBoundaryHighlight) {
          console.log('🗺️ Clearing previous boundaries for refresh');
          props.onBoundaryHighlight([]); // Clear first

          // Add unique timestamp to features to force refresh
          const featuresWithTimestamp = boundaryResult.features.map(feature => ({
            ...feature,
            properties: {
              ...feature.properties,
              _refreshTimestamp: new Date().toISOString()
            }
          }));

          setTimeout(() => {
            console.log('🗺️ Setting new boundaries:', featuresWithTimestamp.length, 'features');
            props.onBoundaryHighlight(featuresWithTimestamp); // Then set new boundaries

            // Update progress - boundaries highlighted
            setAoiLoading(prev => ({ ...prev, progress: 80, zooming: true }));
          }, 100); // Increased delay to ensure clearing happens first
        }

        // Generate AOI preview data for map zoom when administrative selection is complete
        // (even if layers/dates aren't selected yet - this enables map zoom on region changes)
        if (isAdministrativeSelectionComplete()) {
          const selectedLevel = selectedRegions.municipality ? 'municipality' :
                               selectedRegions.district ? 'district' : 'province';

          const selectedName = selectedRegions.municipality ||
                              selectedRegions.district ||
                              selectedRegions.provinceName;

          // Fetch precise geometry for the selected boundary
          const fetchAOIGeometry = async () => {
            try {
              console.log('🎯 Fetching precise geometry for AOI clipping...');

              // First, try using the existing boundary features if available
              if (boundaryResult.features && boundaryResult.features.length > 0) {
                console.log('✅ Using existing boundary features for AOI clipping');
                console.log(`📊 Boundary features available: ${boundaryResult.features.length}`);

                // Use the first feature's geometry (assuming single boundary selection)
                const feature = boundaryResult.features[0];
                const geometry = feature.geometry;

                console.log('🔍 Feature geometry details:', {
                  type: geometry?.type,
                  hasCoordinates: !!(geometry as any)?.coordinates,
                  coordinatesLength: (geometry as any)?.coordinates?.length,
                  featureProperties: Object.keys(feature.properties || {})
                });

                const aoiData = {
                  type: 'administrative' as const,
                  level: selectedLevel,
                  name: selectedName,
                  code: selectedRegions.municipalityCode ||
                        selectedRegions.districtCode ||
                        selectedRegions.province,
                  bounds: (() => {
                    console.log('🔍 Sidebar: boundaryResult.bounds =', boundaryResult.bounds);
                    const finalBounds = boundaryResult.bounds || {
                      north: -22.0,
                      south: -35.0,
                      east: 33.0,
                      west: 16.0
                    };
                    console.log('🔍 Sidebar: finalBounds =', finalBounds);
                    return finalBounds;
                  })(),
                  area: boundaryResult.area || 1000,
                  geometry: geometry,
                  feature: feature,
                  // Add timestamp to ensure uniqueness for debugging
                  timestamp: new Date().toISOString(),
                  // Add selection details for better tracking
                  selectionDetails: {
                    provinceName: selectedRegions.provinceName,
                    district: selectedRegions.district,
                    municipality: selectedRegions.municipality
                  }
                };

                console.log('🎯 Generated AOI data for clipping using boundary features:', {
                  type: aoiData.type,
                  level: aoiData.level,
                  name: aoiData.name,
                  code: aoiData.code,
                  hasGeometry: !!aoiData.geometry,
                  hasFeature: !!aoiData.feature,
                  geometryType: aoiData.geometry?.type,
                  bounds: aoiData.bounds
                });

                // Test WKT conversion to ensure it will work in MapComponent
                if (aoiData.geometry) {
                  try {
                    const { convertGeoJSONToWKT } = await import('../../utils/wktConverter');
                    const testWKT = convertGeoJSONToWKT(aoiData.geometry);
                    console.log('✅ WKT conversion test successful:', {
                      wktLength: testWKT.length,
                      wktPreview: testWKT.substring(0, 100) + '...'
                    });
                  } catch (wktError) {
                    console.error('❌ WKT conversion test failed:', wktError);
                    console.log('⚠️ This will cause CQL filter to fail and fallback to BBOX clipping');
                  }
                }

                // Update local AOI preview and trigger parent callback
                setLocalAoiPreviewData(aoiData);

                if (props.onAOIPreview) {
                  props.onAOIPreview(aoiData);
                }
                return;
              }

              // Fallback: Try to fetch detailed geometry (this might timeout)
              console.log('⚠️ No boundary features available, attempting detailed geometry fetch...');

              const geometryFilters = {
                province: selectedRegions.provinceName,
                district: selectedRegions.district,
                municipality: selectedRegions.municipality
              };

              console.log('🔍 Geometry filters:', geometryFilters);

              const geometryResult = await getSelectedBoundaryGeometry(geometryFilters);

              console.log('🔍 Geometry result:', {
                hasGeometry: !!geometryResult.geometry,
                hasFeature: !!geometryResult.feature,
                hasBounds: !!geometryResult.bounds,
                geometryType: geometryResult.geometry?.type,
                coordinatesLength: (geometryResult.geometry as any)?.coordinates?.length
              });

              if (!geometryResult.geometry) {
                console.warn('⚠️ No geometry retrieved - AOI clipping will use bounds only');
              } else {
                console.log('✅ Retrieved geometry for boundary:', geometryResult.geometry.type);
              }

              const aoiData = {
                type: 'administrative' as const,
                level: selectedLevel,
                name: selectedName,
                code: selectedRegions.municipalityCode ||
                      selectedRegions.districtCode ||
                      selectedRegions.province,
                bounds: geometryResult.bounds || boundaryResult.bounds || {
                  north: -22.0,
                  south: -35.0,
                  east: 33.0,
                  west: 16.0
                },
                area: boundaryResult.area || 1000,
                // Add precise geometry for clipping
                geometry: geometryResult.geometry,
                feature: geometryResult.feature,
                // Add timestamp to ensure uniqueness for debugging
                timestamp: new Date().toISOString(),
                // Add selection details for better tracking
                selectionDetails: {
                  provinceName: selectedRegions.provinceName,
                  district: selectedRegions.district,
                  municipality: selectedRegions.municipality
                }
              };

              console.log('🗺️ Generated AOI preview data with geometry:', aoiData);
              setLocalAoiPreviewData(aoiData);
            } catch (error) {
              console.error('Failed to fetch AOI geometry:', error);

              // Fallback to bounds-only AOI data
              const aoiData = {
                type: 'administrative' as const,
                level: selectedLevel,
                name: selectedName,
                code: selectedRegions.municipalityCode ||
                      selectedRegions.districtCode ||
                      selectedRegions.province,
                bounds: boundaryResult.bounds || {
                  north: -22.0,
                  south: -35.0,
                  east: 33.0,
                  west: 16.0
                },
                area: boundaryResult.area || 1000,
                // Add timestamp to ensure uniqueness for debugging
                timestamp: new Date().toISOString(),
                // Add selection details for better tracking
                selectionDetails: {
                  provinceName: selectedRegions.provinceName,
                  district: selectedRegions.district,
                  municipality: selectedRegions.municipality
                }
              };

              setLocalAoiPreviewData(aoiData);
            }
          };

          fetchAOIGeometry();
        } else {
          // Clear AOI preview if administrative selection is not complete
          setLocalAoiPreviewData(null);
        }

        // Trigger region selection callback for additional handling
        if (props.onBoundaryRegionSelection) {
          props.onBoundaryRegionSelection(boundaryResult.features);
        }

        // Complete the loading process after a short delay to show the final zoom
        setTimeout(() => {
          setAoiLoading({ highlighting: false, zooming: false, progress: 100 });
          console.log('✅ AOI loading process completed');
        }, 1000); // Allow time for map zoom to complete

      } else {
        console.log('No boundary features found for current selection');
        if (props.onBoundaryHighlight) {
          props.onBoundaryHighlight([]);
        }
        setLocalAoiPreviewData(null);
        setAoiLoading({ highlighting: false, zooming: false, progress: 0 });
      }
    } catch (error) {
      console.error('Failed to trigger boundary updates:', error);
      if (props.onBoundaryHighlight) {
        props.onBoundaryHighlight([]);
      }
      setLocalAoiPreviewData(null);
      setAoiLoading({ highlighting: false, zooming: false, progress: 0 });
    }
  }, [
    selectedRegions.provinceName,
    selectedRegions.district,
    selectedRegions.municipality,
    selectedRegions.municipalityCode,
    selectedRegions.districtCode,
    selectedRegions.province,
    isAdministrativeSelectionComplete
    // Removed props.onBoundaryHighlight and props.onBoundaryRegionSelection to prevent circular dependencies
  ]);

  useEffect(() => {
    // Debounce the updates to avoid excessive API calls
    const debounceTimer = setTimeout(triggerBoundaryUpdates, 300);
    return () => clearTimeout(debounceTimer);
  }, [triggerBoundaryUpdates]);

  // Auto-expand date range section when administrative selection is complete
  useEffect(() => {
    if (isAdministrativeSelectionComplete() && !nestedSections.dateRange) {
      setNestedSections(prev => ({ ...prev, dateRange: true }));
    }
  }, [isAdministrativeSelectionComplete, nestedSections.dateRange]);

  // Separate useEffect to update AOI preview when administrative selection becomes complete
  useEffect(() => {
    // Update AOI preview when administrative selection is complete for map zoom
    // (even if layers/dates aren't selected yet)
    if (isAdministrativeSelectionComplete() && !localAoiPreviewData) {
      // Check if we have sufficient data to generate AOI preview
      if (selectedRegions.provinceName) {
        const selectedLevel = selectedRegions.municipality ? 'municipality' :
                             selectedRegions.district ? 'district' : 'province';

        const selectedName = selectedRegions.municipality ||
                            selectedRegions.district ||
                            selectedRegions.provinceName;

        const aoiData = {
          type: 'administrative' as const,
          level: selectedLevel,
          name: selectedName,
          code: selectedRegions.municipalityCode ||
                selectedRegions.districtCode ||
                selectedRegions.province,
          bounds: {
            north: -22.0,
            south: -35.0,
            east: 33.0,
            west: 16.0
          },
          area: 1000,
          // Add timestamp to ensure uniqueness for debugging
          timestamp: new Date().toISOString(),
          // Add selection details for better tracking
          selectionDetails: {
            provinceName: selectedRegions.provinceName,
            district: selectedRegions.district,
            municipality: selectedRegions.municipality
          }
        };

        console.log('🎯 Layer selection complete - generating AOI preview:', aoiData);
        setLocalAoiPreviewData(aoiData);
      }
    } else if (!isAdministrativeSelectionComplete() && localAoiPreviewData) {
      // Clear AOI preview if administrative selection is no longer complete
      setLocalAoiPreviewData(null);
    }
  }, [isAdministrativeSelectionComplete, selectedRegions.provinceName, selectedRegions.district, selectedRegions.municipality, localAoiPreviewData]);

  // Load sidebar state from localStorage
  useEffect(() => {
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState) {
      setIsCollapsed(savedState === 'true');
    }
  }, []);

  // Save sidebar state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', isCollapsed.toString());
  }, [isCollapsed]);

  // Effect to update coordinates from props - handled by the input directly now
  // No local state needed since we use props.currentCoordinates directly

  // Note: Coordinate section is now always visible, no need to handle section state

  // Toggle sidebar collapse state
  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Let's also dispatch an event when the sidebar state changes
  useEffect(() => {
    window.dispatchEvent(
      new CustomEvent('sidebarToggle', {
        detail: { collapsed: isCollapsed }
      })
    );
  }, [isCollapsed]);

  // Check if any temporal layers are selected
  const hasTemporalLayers = props.selectedLayerNames.includes('soilMoisture');
  const getSelectedLayerName = () => (hasTemporalLayers ? 'Soil Moisture' : '');

  function onSearch(query: string): void {
    if (props.onSearch) {
      props.onSearch(query);
    }
  }

  function onDrawModeToggle(isDrawing: boolean): void {
    if (props.onDrawModeToggle) {
      props.onDrawModeToggle(isDrawing);
    }
  }

  function onClearDrawnArea(): void {
    if (props.onClearDrawnArea) {
      props.onClearDrawnArea();
    }
  }

  function onAOIMethodChange(method: 'drawn' | 'regional'): void {
    if (props.onAOIMethodChange) {
      props.onAOIMethodChange(method);
    }
  }

  function onConfigureRegions(): void {
    if (props.onConfigureRegions) {
      props.onConfigureRegions();
    }
  }

  // Generate AOI preview data for administrative boundaries
  const generateAOIPreview = (level: 'province' | 'district' | 'municipality', name: string, code?: string) => {
    console.log('🔧 generateAOIPreview called - this should use actual bounds, not hardcoded ones');

    // Use the actual calculated bounds from props.aoiPreviewData if available
    const actualBounds = props.aoiPreviewData?.bounds;

    if (actualBounds) {
      console.log('✅ Using actual calculated bounds:', actualBounds);
      const aoiData = {
        type: 'administrative' as const,
        level,
        name,
        code,
        bounds: actualBounds,
        area: props.aoiPreviewData.area || 1000,
        geometry: props.aoiPreviewData.geometry,
        feature: props.aoiPreviewData.feature
      };
      setLocalAoiPreviewData(aoiData);
    } else {
      console.warn('⚠️ No actual bounds available, using fallback bounds');
      // Fallback to rough bounds only if no actual bounds available
      const roughBounds = {
        north: -22.0,
        south: -35.0,
        east: 33.0,
        west: 16.0
      };

      const areaMap = {
        province: 120000,
        district: 25000,
        municipality: 8000
      };

      const aoiData = {
        type: 'administrative' as const,
        level,
        name,
        code,
        bounds: roughBounds,
        area: areaMap[level] || 1000,
        // Add timestamp to ensure uniqueness for debugging
        timestamp: new Date().toISOString(),
        // Add selection details for better tracking
        selectionDetails: {
          provinceName: selectedRegions.provinceName,
          district: selectedRegions.district,
          municipality: selectedRegions.municipality
        }
      };
      setLocalAoiPreviewData(aoiData);
    }
  };

  const handleDownloadAOI = (selectedLayers: string[], aoiData: any) => {
    // Call the parent's download handler if available
    if (props.onAOIDownload) {
      props.onAOIDownload(selectedLayers, aoiData);
    } else {
      alert(`Download initiated!\n\nArea: ${aoiData.name}\nLayers: ${selectedLayers.length}\nLevel: ${aoiData.level}`);
    }
  };

  function onClearRegionalSelection(): void {
    // Clear all selected administrative regions and boundaries
    setSelectedRegions({
      province: '',
      provinceName: '',
      municipality: '',
      municipalityCode: '',
      district: '',
      districtCode: ''
    });
    setAdministrativeBoundaries(prev => ({
      ...prev,
      municipalities: [],
      districts: []
    }));

    // Clear map highlighting
    if (props.onBoundaryHighlight) {
      props.onBoundaryHighlight([]);
    }

    // Clear AOI preview data to trigger map zoom back to South Africa
    setLocalAoiPreviewData(null);
    if (props.onAOIPreview) {
      props.onAOIPreview(null);
    }

    // Clear date range selections
    props.onDateChange('startDate', '');
    props.onDateChange('endDate', '');

    console.log('🔄 Reset: Cleared all selections, map highlighting, and date range');
  }

  function onClearProvince(): void {
    // Can only clear province if no district is selected
    if (selectedRegions.district) {
      console.log('⚠️ Cannot clear province while district is selected');
      return;
    }

    // Clear province and all dependent selections
    setSelectedRegions({
      province: '',
      provinceName: '',
      municipality: '',
      municipalityCode: '',
      district: '',
      districtCode: ''
    });
    setAdministrativeBoundaries(prev => ({
      ...prev,
      municipalities: [],
      districts: []
    }));

    // Clear map highlighting
    if (props.onBoundaryHighlight) {
      props.onBoundaryHighlight([]);
    }

    // Clear AOI preview data to trigger map zoom back to South Africa
    setLocalAoiPreviewData(null);
    if (props.onAOIPreview) {
      props.onAOIPreview(null);
    }

    console.log('🔄 Province cleared: Reset to South Africa view');
  }

  function onClearDistrict(): void {
    // Clear district and dependent selections, but keep province
    setSelectedRegions(prev => ({
      ...prev,
      municipality: '',
      municipalityCode: '',
      district: '',
      districtCode: ''
    }));
    setAdministrativeBoundaries(prev => ({
      ...prev,
      municipalities: []
    }));

    // Keep province highlighting but clear district-specific highlighting
    // This will trigger a re-zoom to province level
    console.log('🔄 District cleared: Zooming back to province view');

    // Trigger boundary updates to zoom back to province level
    setTimeout(() => {
      triggerBoundaryUpdates();
    }, 100);
  }

  function onPredefinedPolygon(size: string): void {
    if (props.onPredefinedPolygon) {
      props.onPredefinedPolygon(size);
    }
  }

  async function onPreviewData(): Promise<void> {
    console.log('🔍 Preview Data requested');

    // Gate preview until explicitly requested
    setPreviewRequested(true);

    // Validate that necessary data is available
    if (!isLayerSelectionComplete()) {
      console.warn('⚠️ Cannot preview data - layer selection not complete');
      alert('Please complete the selection process:\n- Select administrative region\n- Choose date range\n- Select at least one data layer');
      return;
    }

    // Build AOI data for preview
    try {
      const selectedLevel = selectedRegions.municipality ? 'municipality' :
                           selectedRegions.district ? 'district' : 'province';

      const selectedName = selectedRegions.municipality ||
                          selectedRegions.district ||
                          selectedRegions.provinceName;

      console.log('🎯 Building AOI data for preview with geometry fetch...');
      console.log('Selected regions:', selectedRegions);
      console.log('Local AOI preview data:', localAoiPreviewData);

      // If we don't have geometry data, fetch it now
      if (!localAoiPreviewData?.geometry) {
        console.log('⚠️ No geometry in localAoiPreviewData, fetching now...');
        await triggerBoundaryUpdates();

        // Wait a moment for the async update to complete
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Create comprehensive AOI data for preview
      const aoiData = {
        type: 'administrative' as const,
        level: selectedLevel,
        name: selectedName,
        code: selectedRegions.municipalityCode ||
              selectedRegions.districtCode ||
              selectedRegions.province,
        bounds: props.aoiPreviewData?.bounds || {
          north: -22.0,
          south: -35.0,
          east: 33.0,
          west: 16.0
        },
        area: props.aoiPreviewData?.area || 1000,
        geometry: props.aoiPreviewData?.geometry,
        feature: props.aoiPreviewData?.feature,
        dateRange: props.dateRange,
        selectedLayers: props.selectedLayerNames,
        temporal: {
          startDate: props.dateRange.startDate,
          endDate: props.dateRange.endDate,
          hasTemporalLayers: props.selectedLayerNames.includes('soilMoisture')
        },
        // Add timestamp to ensure uniqueness for debugging
        timestamp: new Date().toISOString(),
        // Add region selection info for debugging
        provinceName: selectedRegions.provinceName,
        district: selectedRegions.district,
        municipality: selectedRegions.municipality
      };

      console.log('🎯 Previewing AOI data:', aoiData);

      // Trigger preview callback to parent component
      if (props.onAOIPreview) {
        props.onAOIPreview(aoiData);
        console.log('✅ AOI preview data sent to parent component');
      } else {
        console.warn('⚠️ No onAOIPreview callback available');
      }

    } catch (error) {
      console.error('❌ Failed to generate preview data:', error);
      alert('Failed to generate preview data. Please check your selections and try again.');
    }
  }

  function onDownloadData(): void {
    console.log('📥 Download Data requested');

    // Validate that necessary data is available
    if (!isLayerSelectionComplete() || !props.aoiPreviewData) {
      console.warn('⚠️ Cannot download data - prerequisites not met');
      alert('Please complete the selection process and preview data before downloading:\n- Select administrative region\n- Choose date range\n- Select at least one data layer\n- Preview the data first');
      return;
    }

    try {
      // Prepare download data
      const downloadData = {
        aoi: props.aoiPreviewData,
        layers: props.selectedLayerNames,
        dateRange: props.dateRange,
        format: 'GeoTIFF', // Default format
        projection: 'EPSG:4326', // WGS84
        timestamp: new Date().toISOString()
      };

      console.log('📦 Preparing download:', downloadData);

      // Trigger download via parent component callback
      if (props.onAOIDownload) {
        props.onAOIDownload(props.selectedLayerNames, props.aoiPreviewData);
        console.log('✅ Download request sent to parent component');
      } else {
        console.warn('⚠️ No download callback available');
        // Fallback: trigger browser download
        const blob = new Blob([JSON.stringify(downloadData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `aoi-data-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        console.log('✅ Fallback download triggered');
      }

    } catch (error) {
      console.error('❌ Failed to initiate download:', error);
      alert('Failed to initiate download. Please try again.');
    }
  }

  function onQueryTemporalData(): void {
    console.log('🕐 Temporal Data Query requested');

    // Check if temporal layers are selected
    const hasTemporalLayers = props.selectedLayerNames.includes('soilMoisture');

    if (!hasTemporalLayers) {
      console.warn('⚠️ No temporal layers selected');
      alert('Please select temporal data layers (e.g., Soil Moisture) to query temporal data.');
      return;
    }

    // Validate date range
    if (!props.dateRange.startDate || !props.dateRange.endDate) {
      console.warn('⚠️ Date range not specified');
      alert('Please specify a date range for temporal data query.');
      return;
    }

    try {
      // Prepare temporal query parameters
      const temporalQuery = {
        layers: props.selectedLayerNames.filter(layer => layer === 'soilMoisture'), // Only temporal layers
        dateRange: props.dateRange,
        aoi: localAoiPreviewData,
        queryType: 'timeSeries',
        aggregation: 'mean', // Default aggregation
        format: 'CSV'
      };

      console.log('📊 Temporal query parameters:', temporalQuery);

      // Trigger temporal query via parent component
      if (props.onQueryTemporalData) {
        props.onQueryTemporalData();
        console.log('✅ Temporal query callback triggered');
      } else {
        console.warn('⚠️ No temporal query callback available');

        // Fallback: show temporal data info
        const info = `Temporal Data Query:\n` +
                    `Layers: ${temporalQuery.layers.join(', ')}\n` +
                    `Date Range: ${props.dateRange.startDate} to ${props.dateRange.endDate}\n` +
                    `AOI: ${localAoiPreviewData?.name || 'Selected Region'}\n` +
                    `\nThis would query time-series data for the selected parameters.`;

        alert(info);
        console.log('ℹ️ Temporal query info displayed');
      }

    } catch (error) {
      console.error('❌ Failed to execute temporal query:', error);
      alert('Failed to execute temporal data query. Please try again.');
    }
  }

  return (
    <div className={`sidebar ${isCollapsed ? 'collapsed' : ''}`}>
      <div className="sidebar-header" style={{ display: 'flex', alignItems: 'center', justifyContent: isCollapsed ? 'center' : 'space-between', height: 56 }}>
        {!isCollapsed && (
          <h1 className="app-title" style={{ margin: 0, flex: 1 }}>DATA ACCESS</h1>
        )}
        <button
          className="sidebar-toggle"
          onClick={toggleSidebar}
          aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          style={isCollapsed ? { margin: 0 } : { marginLeft: 8 }}
        >
          {isCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
        </button>
      </div>
      {!isCollapsed && (
        <div className="sidebar-content">
          {/* Select Region of Interest Toggle */}
          <div className="sidebar-card">
            <div className="sidebar-card-body">
              <div className="d-flex align-items-center justify-content-between mb-3">
                <div className="d-flex align-items-center">
                  <span className="text-danger me-2">📍</span>
                  <span style={{ fontSize: '0.9rem', fontWeight: '500' }}>Select region of interest</span>
                </div>
                <Form.Check
                  type="switch"
                  id="region-toggle"
                  checked={selectRegionOfInterest}
                  onChange={(e) => setSelectRegionOfInterest(e.target.checked)}
                  className="custom-switch"
                />
              </div>
            </div>
          </div>

          {/* Region of Interest Card - Only show when toggle is ON */}
          {selectRegionOfInterest && (
            <div className="sidebar-card">
              <div className="sidebar-card-body">
                {/* Administrative Boundaries and Date Controls - Consolidated */}
                <div className="mb-3">
                      {/* Reset Button - Always show at top when any selection is made */}
                      {(selectedRegions.provinceName || selectedRegions.district || selectedRegions.municipality) && (
                        <div className="mb-3">
                          <button
                            className="btn btn-sm btn-outline-light"
                            onClick={onClearRegionalSelection}
                            style={{
                              fontSize: '0.8rem',
                              padding: '4px 12px',
                              borderColor: 'rgba(255, 255, 255, 0.3)',
                              color: 'white',
                              backgroundColor: 'transparent'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                              e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.5)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = 'transparent';
                              e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.3)';
                            }}
                          >
                            Reset Selection
                          </button>
                        </div>
                      )}

                      <Form.Group className="mb-2">
                        <div className="d-flex align-items-center justify-content-between">
                          <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white', margin: 0 }}>
                            Province:
                          </Form.Label>
                          {selectedRegions.province && !selectedRegions.district && (
                            <button
                              onClick={onClearProvince}
                              style={{
                                background: 'none',
                                border: 'none',
                                color: 'rgba(255, 255, 255, 0.7)',
                                cursor: 'pointer',
                                padding: '2px',
                                display: 'flex',
                                alignItems: 'center'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.color = '#ff6b6b';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.color = 'rgba(255, 255, 255, 0.7)';
                              }}
                              title="Clear province selection"
                            >
                              <Trash2 size={14} />
                            </button>
                          )}
                        </div>
                        <Form.Select
                          size="sm"
                          style={{
                            fontSize: '0.85rem',
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            border: '1px solid rgba(255, 255, 255, 0.3)',
                            color: '#333'
                          }}
                          value={selectedRegions.province}
                          onChange={(e) => handleRegionChange('province', e.target.value)}
                          disabled={boundaryLoading.provinces}
                        >
                          <option value="">
                            {boundaryLoading.provinces ? '-- Loading Provinces --' : '-- Select Province --'}
                          </option>
                          {administrativeBoundaries.provinces.map((province) => (
                            <option key={province.id} value={province.id}>
                              {province.name}
                            </option>
                          ))}
                        </Form.Select>
                        {/* Loader for districts below province dropdown */}
                        {selectedRegions.province && boundaryLoading.districts && (
                          <div style={{ fontSize: '0.8rem', color: '#ffc107', marginTop: 4 }}>Loading districts...</div>
                        )}
                        {/* AOI Loading Progress for Province */}
                        {selectedRegions.province && aoiLoading.highlighting && (
                          <div style={{ marginTop: '8px' }}>
                            <div style={{
                              fontSize: '0.75rem',
                              color: 'rgba(255, 255, 255, 0.8)',
                              marginBottom: '4px',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '6px'
                            }}>
                              <div
                                className="spinner-border spinner-border-sm"
                                role="status"
                                style={{ width: '12px', height: '12px', borderWidth: '1px' }}
                              />
                              Loading province boundaries... {aoiLoading.progress}%
                            </div>
                            <div style={{
                              width: '100%',
                              height: '3px',
                              backgroundColor: 'rgba(255, 255, 255, 0.2)',
                              borderRadius: '2px',
                              overflow: 'hidden'
                            }}>
                              <div style={{
                                width: `${aoiLoading.progress}%`,
                                height: '100%',
                                backgroundColor: aoiLoading.zooming ? '#28a745' : '#007bff',
                                transition: 'width 0.3s ease, background-color 0.3s ease'
                              }} />
                            </div>
                          </div>
                        )}
                      </Form.Group>

                      {/* Show District dropdown only if province is selected and districts loaded */}
                      {selectedRegions.province && !boundaryLoading.districts && administrativeBoundaries.districts.length > 0 && (
                        <Form.Group className="mb-2">
                          <div className="d-flex align-items-center justify-content-between">
                            <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white', margin: 0 }}>
                              Municipality/District:
                            </Form.Label>
                            {selectedRegions.district && (
                              <button
                                onClick={onClearDistrict}
                                style={{
                                  background: 'none',
                                  border: 'none',
                                  color: 'rgba(255, 255, 255, 0.7)',
                                  cursor: 'pointer',
                                  padding: '2px',
                                  display: 'flex',
                                  alignItems: 'center'
                                }}
                                onMouseEnter={(e) => {
                                  e.currentTarget.style.color = '#ff6b6b';
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.style.color = 'rgba(255, 255, 255, 0.7)';
                                }}
                                title="Clear district/municipality selection"
                              >
                                <Trash2 size={14} />
                              </button>
                            )}
                          </div>
                          <Form.Select
                            size="sm"
                            style={{
                              fontSize: '0.85rem',
                              backgroundColor: 'rgba(255, 255, 255, 0.95)',
                              border: '1px solid rgba(255, 255, 255, 0.3)',
                              color: '#333'
                            }}
                            value={selectedRegions.district}
                            onChange={(e) => handleRegionChange('district', e.target.value)}
                          >
                            <option value="">-- Select Municipality/District --</option>
                            {administrativeBoundaries.districts.map((district) => (
                              <option key={district.id} value={district.name}>
                                {district.name} {district.properties?.isMetro ? '🟦 Metro' : '🟩 District'}
                              </option>
                            ))}
                          </Form.Select>
                          {/* Loader for municipalities below district dropdown */}
                          {selectedRegions.district && boundaryLoading.municipalities && (
                            <div style={{ fontSize: '0.8rem', color: '#ffc107', marginTop: 4 }}>Loading municipalities...</div>
                          )}
                          {/* AOI Loading Progress for District */}
                          {selectedRegions.district && aoiLoading.highlighting && (
                            <div style={{ marginTop: '8px' }}>
                              <div style={{
                                fontSize: '0.75rem',
                                color: 'rgba(255, 255, 255, 0.8)',
                                marginBottom: '4px',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '6px'
                              }}>
                                <div
                                  className="spinner-border spinner-border-sm"
                                  role="status"
                                  style={{ width: '12px', height: '12px', borderWidth: '1px' }}
                                />
                                {aoiLoading.zooming ? 'Zooming to district...' : 'Loading district boundaries...'} {aoiLoading.progress}%
                              </div>
                              <div style={{
                                width: '100%',
                                height: '3px',
                                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                                borderRadius: '2px',
                                overflow: 'hidden'
                              }}>
                                <div style={{
                                  width: `${aoiLoading.progress}%`,
                                  height: '100%',
                                  backgroundColor: aoiLoading.zooming ? '#28a745' : '#007bff',
                                  transition: 'width 0.3s ease, background-color 0.3s ease'
                                }} />
                              </div>
                            </div>
                          )}
                        </Form.Group>
                      )}

                      {/* Date Controls - Show immediately after province selection */}
                      {selectedRegions.province && (
                        <>
                          <Form.Group className="mb-2">
                            <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>Start Date:</Form.Label>
                            <Form.Control
                              type="date"
                              value={props.dateRange.startDate.split('/').join('-')}
                              onChange={(e) => {
                                const date = e.target.value.split('-').join('/');
                                props.onDateChange('startDate', date);
                              }}
                              size="sm"
                              style={{
                                fontSize: '0.85rem',
                                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                border: '1px solid rgba(255, 255, 255, 0.3)',
                                color: '#333'
                              }}
                            />
                          </Form.Group>

                          <Form.Group className="mb-2">
                            <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>End Date:</Form.Label>
                            <Form.Control
                              type="date"
                              value={props.dateRange.endDate.split('/').join('-')}
                              onChange={(e) => {
                                const date = e.target.value.split('-').join('/');
                                props.onDateChange('endDate', date);
                              }}
                              size="sm"
                              style={{
                                fontSize: '0.85rem',
                                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                border: '1px solid rgba(255, 255, 255, 0.3)',
                                color: '#333'
                              }}
                            />
                          </Form.Group>

                          {/* Selected Date Range Display */}
                          {props.dateRange.startDate && props.dateRange.endDate && (
                            <div className="mb-2 p-2" style={{
                              backgroundColor: 'rgba(255, 255, 255, 0.1)',
                              borderRadius: '4px',
                              border: '1px solid rgba(255, 255, 255, 0.2)'
                            }}>
                              <div style={{ fontSize: '0.75rem', color: 'rgba(255, 255, 255, 0.8)', marginBottom: '4px' }}>
                                Selected Date Range:
                              </div>
                              <div style={{ fontSize: '0.85rem', color: 'white', fontWeight: '500' }}>
                                📅 {new Date(props.dateRange.startDate.replace(/\//g, '-')).toLocaleDateString()} - {new Date(props.dateRange.endDate.replace(/\//g, '-')).toLocaleDateString()}
                              </div>
                              <div style={{ fontSize: '0.75rem', color: 'rgba(255, 255, 255, 0.7)', marginTop: '2px' }}>
                                {(() => {
                                  const start = new Date(props.dateRange.startDate.replace(/\//g, '-'));
                                  const end = new Date(props.dateRange.endDate.replace(/\//g, '-'));
                                  const diffTime = Math.abs(end.getTime() - start.getTime());
                                  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                                  return `Duration: ${diffDays} day${diffDays !== 1 ? 's' : ''}`;
                                })()}
                              </div>
                            </div>
                          )}
                        </>
                      )}

                      {/* Show Municipality dropdown if district is selected and municipalities loaded */}
                      {selectedRegions.district && !boundaryLoading.municipalities && administrativeBoundaries.municipalities.length > 0 && (
                        <Form.Group className="mb-2">
                          <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>Local Municipality:</Form.Label>
                          <Form.Select
                            size="sm"
                            style={{
                              fontSize: '0.85rem',
                              backgroundColor: 'rgba(255, 255, 255, 0.95)',
                              border: '1px solid rgba(255, 255, 255, 0.3)',
                              color: '#333'
                            }}
                            value={selectedRegions.municipalityCode}
                            onChange={(e) => handleRegionChange('municipality', e.target.value)}
                          >
                            <option value="">-- Select Municipality --</option>
                            {administrativeBoundaries.municipalities.map((municipality) => (
                              <option key={municipality.id} value={municipality.code}>
                                {municipality.name}
                              </option>
                            ))}
                          </Form.Select>
                        </Form.Group>
                      )}

                </div>



                {/* Coordinates Input Section */}
                <div className="mb-3">
                      <Form.Group className="mb-2">
                        <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>Enter Coordinates:</Form.Label>
                        <div className="d-flex">
                          <Form.Control
                            type="text"
                            placeholder="latitude, longitude"
                            size="sm"
                            style={{ fontSize: '0.85rem' }}
                            value={props.currentCoordinates || ''}
                            readOnly={true}
                          />
                          <button
                            className={`btn btn-sm ms-2 ${isPinningMode ? 'btn-danger' : 'btn-primary'}`}
                            onClick={() => {
                              const newMode = !isPinningMode;
                              console.log('Pin button clicked - current isPinningMode:', isPinningMode, 'newMode:', newMode);
                              setIsPinningMode(newMode);
                              // Toggle pin mode in parent component
                              if (props.onCoordinatePinModeToggle) {
                                console.log('Calling onCoordinatePinModeToggle with:', newMode);
                                props.onCoordinatePinModeToggle(newMode);
                              }
                            }}
                            title={isPinningMode ? 'Cancel pin placement' : 'Place pin on map'}
                          >
                            {isPinningMode ? '✕' : '📌'}
                          </button>
                        </div>
                        <small className={`d-block mt-1 ${isPinningMode ? 'text-warning' : 'text-muted'}`}>
                          {isPinningMode
                            ? 'Click on map to place a pin at desired location'
                            : 'Example: -26.2041, 28.0473 or click the pin button'}
                        </small>
                      </Form.Group>
                </div>

                {/* Drawing Tools Section */}
                <div className="nested-section mb-0">
                  <div
                    className="nested-section-header clickable"
                    onClick={() => toggleNestedSection('drawingTools')}
                  >
                    <div className="d-flex align-items-center">
                      <span className="nested-section-icon">✏️</span>
                      <span className="nested-section-title">Drawing Tools</span>
                    </div>
                    <span className="nested-section-toggle">
                      {nestedSections.drawingTools ? '▼' : '▶'}
                    </span>
                  </div>
                  {nestedSections.drawingTools && (
                    <div className="nested-section-body">
                      <RegionSelector
                        onSearch={onSearch}
                        onDrawModeToggle={onDrawModeToggle}
                        isDrawingMode={props.isDrawingMode}
                        hasDrawnArea={props.hasDrawnArea}
                        onClearDrawnArea={onClearDrawnArea}
                        aoiMethod={props.aoiMethod}
                        onAOIMethodChange={onAOIMethodChange}
                        hasRegionalSelection={props.hasRegionalSelection}
                        onConfigureRegions={onConfigureRegions}
                        onClearRegionalSelection={onClearRegionalSelection}
                        onPredefinedPolygon={onPredefinedPolygon}
                        onBoundaryHighlight={props.onBoundaryHighlight}
                        onBoundaryRegionSelection={props.onBoundaryRegionSelection}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Data Layers Card - Always visible for discovery */}
          <div className="sidebar-card">
            <div className="sidebar-card-header">
              <h5 className="sidebar-card-title">
                Data Layers

              </h5>
            </div>
            <div className="sidebar-card-body">




              <DataLayers
                layers={props.layers}
                selectedLayerNames={props.selectedLayerNames}
                onLayerChange={props.onLayerChange}
                isLoading={props.isLoading}
                error={props.error}
                selectedBasemap={props.selectedBasemap}
                onBasemapChange={props.onBasemapChange}
                layerOpacities={props.layerOpacities}
                onOpacityChange={props.onOpacityChange}
              />
            </div>
          </div>

          {/* Service Details Card */}
          {/* <div className="sidebar-card">
            <div className="sidebar-card-header">
              <h5 className="sidebar-card-title">Layer Information</h5>
            </div>
            <div className="sidebar-card-body">
              <ServiceDetails
                selectedLayers={props.selectedLayerNames.reduce((acc, name) => {
                  acc[name] = true;
                  return acc;
                }, {} as Record<string, boolean>)}
              />
            </div>
          </div> */}

          {/* Actions Card - Only show when layers are selected */}
          {isLayerSelectionComplete() && (
            <div className="sidebar-card">
              <div className="sidebar-card-header">
                <h5 className="sidebar-card-title">Actions</h5>
              </div>
              <div className="sidebar-card-body">
                {/* Analysis Summary */}
                <div style={{
                  fontSize: '0.85rem',
                  marginBottom: '16px',
                  padding: '12px',
                  backgroundColor: 'rgba(0, 123, 255, 0.05)',
                  borderRadius: '6px',
                  border: '1px solid rgba(0, 123, 255, 0.2)'
                }}>
                  <div style={{ color: '#0056b3', fontWeight: 'bold', marginBottom: '8px' }}>
                    🚀 Analysis Summary
                  </div>

                  {/* Boundary Selection Summary */}
                  <div style={{ marginBottom: '8px' }}>
                    <strong>📍 Area:</strong> {' '}
                    {selectedRegions.municipality ? `${selectedRegions.municipality} (Municipality)` :
                     selectedRegions.district ? `${selectedRegions.district} (District)` :
                     selectedRegions.provinceName ? `${selectedRegions.provinceName} (Province)` :
                     localAoiPreviewData?.name || 'Custom Area'}
                  </div>

                  {/* Date Range Summary */}
                  <div style={{ marginBottom: '8px' }}>
                    <strong>📅 Period:</strong> {' '}
                    {props.dateRange.startDate && props.dateRange.endDate ?
                      `${new Date(props.dateRange.startDate).toLocaleDateString()} - ${new Date(props.dateRange.endDate).toLocaleDateString()}` :
                      'Date range selected'}
                  </div>

                  {/* Layers Summary */}
                  <div>
                    <strong>🗂️ Layers:</strong> {' '}
                    {props.selectedLayerNames.length > 0 ?
                      `${props.selectedLayerNames.length} layer${props.selectedLayerNames.length !== 1 ? 's' : ''} selected` :
                      'No layers selected'}
                  </div>

                  {props.selectedLayerNames.length > 0 && (
                    <div style={{ marginTop: '6px', fontSize: '0.75rem', color: '#6c757d' }}>
                      {props.selectedLayerNames.slice(0, 3).join(', ')}
                      {props.selectedLayerNames.length > 3 && ` + ${props.selectedLayerNames.length - 3} more`}
                    </div>
                  )}
                </div>
                <DataActions
                  onPreviewData={onPreviewData}
                  onDownloadData={onDownloadData}
                  onQueryTemporalData={hasTemporalLayers ? onQueryTemporalData : undefined}
                  temporalLayerName={getSelectedLayerName()}
                />

                {/* AOI Preview Card - Show only after user clicks Preview */}
                {previewRequested && isLayerSelectionComplete() && props.aoiPreviewData && (
                  <div className="mt-3">
                    <AOIPreviewCard
                      aoiData={props.aoiPreviewData}
                      selectedLayers={props.selectedLayerNames}
                      selectedBasemap={props.selectedBasemap}
                      onDownload={handleDownloadAOI}
                    />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Sidebar;