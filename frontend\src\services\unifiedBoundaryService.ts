import { debugWardLoading, debugLayerExistence } from "./wardDebug";
import axios from 'axios';
import { API_CONFIG } from '../config';

// Unified Administrative Boundary Service
// Consolidates legacy administrativeBoundaryService and Interactive Boundary Filter functionality

export interface AdministrativeRegion {
  id: string;
  name: string;
  code?: string;
  properties?: Record<string, any>;
}

export interface BoundaryFilterResponse {
  features: GeoJSON.Feature[];
  totalCount: number;
  isPartial: boolean;
  bounds?: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  area?: number;
}

export interface FilterOption {
  value: string;
  label: string;
  metadata?: {
    id?: string;
    code?: string;
    type?: string;
    isMetro?: boolean;
    [key: string]: any;
  };
}

export interface BoundaryFilters {
  province?: string;
  district?: string;
  municipality?: string;
  ward?: string;
}

// Cache for administrative boundary data
const boundaryCache = new Map<string, any>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Administrative boundary layer configuration
const BOUNDARY_LAYER = 'geonode:south_africa_municipal_boundaries';

/**
 * Security: Sanitize CQL filter values to prevent injection
 */
export const sanitizeCQLValue = (value: string): string => {
  if (!value || typeof value !== 'string') return '';

  // Remove potentially dangerous characters and patterns
  return value
    .replace(/['"\\]/g, '') // Remove quotes and backslashes
    .replace(/[;&|]/g, '')  // Remove command separators
    .replace(/[<>]/g, '')   // Remove comparison operators
    .replace(/\b(DROP|DELETE|INSERT|UPDATE|CREATE|ALTER|EXEC|EXECUTE)\b/gi, '') // Remove SQL keywords
    .trim()
    .slice(0, 100); // Limit length
};

/**
 * Build CQL filter string from boundary filters
 * Adapts to different layers: municipal boundaries or ward boundaries
 */
export const buildCQLFilter = (filters: BoundaryFilters, isWardLayer: boolean = false): string => {
  const conditions: string[] = [];

  if (isWardLayer) {
    // For ward layer (wards 2020), use ward-specific field names
    if (filters.ward) {
      const sanitized = sanitizeCQLValue(filters.ward);
      // Match by ward label or ward number
      conditions.push(`wardlabel='${sanitized}' OR wardno='${sanitized}'`);
    }

    if (filters.municipality) {
      const sanitized = sanitizeCQLValue(filters.municipality);
      // Match by municipality name or code
      conditions.push(`municipali ILIKE '%${sanitized}%' OR cat_b='${sanitized}'`);
    }

    if (filters.district) {
      const sanitized = sanitizeCQLValue(filters.district);
      conditions.push(`district ILIKE '%${sanitized}%' OR districtco='${sanitized}'`);
    }

    if (filters.province) {
      const sanitized = sanitizeCQLValue(filters.province);
      conditions.push(`province='${sanitized}'`);
    }
  } else {
    // For municipal boundaries layer (original logic)
    if (filters.province) {
      const sanitized = sanitizeCQLValue(filters.province);
      conditions.push(`adm1_en='${sanitized}'`);
    }

    // The adm2_en field contains both districts and municipalities
    if (filters.district) {
      const sanitized = sanitizeCQLValue(filters.district);
      conditions.push(`adm2_en='${sanitized}'`);
    }

    if (filters.municipality) {
      const sanitized = sanitizeCQLValue(filters.municipality);
      conditions.push(`adm2_en='${sanitized}'`);
    }

    // If ward filter is requested but we're using the municipal layer
    if (filters.ward) {
      console.log('Ward filtering requested on municipal layer. Consider using ward layer instead.');
    }
  }

  return conditions.join(' AND ');
};

/**
 * Calculate bounds from GeoJSON features
 */
export const calculateBounds = (features: GeoJSON.Feature[]): BoundaryFilterResponse['bounds'] => {
  console.log(`📐 calculateBounds called with ${features.length} features`);
  if (!features.length) {
    console.log(`⚠️ No features provided for bounds calculation`);
    return undefined;
  }

  let minLng = Infinity, maxLng = -Infinity;
  let minLat = Infinity, maxLat = -Infinity;

  features.forEach((feature, index) => {
    console.log(`📐 Processing feature ${index + 1}: geometry type = ${feature.geometry?.type || 'null'}`);
    if (feature.geometry) {
      let allCoords: number[][] = [];

      // Handle different geometry types
      if (feature.geometry.type === 'Polygon') {
        console.log(`📐 Processing Polygon geometry`);
      } else if (feature.geometry.type === 'MultiPolygon') {
        console.log(`📐 Processing MultiPolygon geometry`);
      } else {
        console.log(`⚠️ Unknown geometry type: ${feature.geometry.type}`);
      }

      if (feature.geometry.type === 'Polygon') {
        // For Polygon: coordinates[0] is the outer ring
        allCoords = (feature.geometry as any).coordinates[0];
      } else if (feature.geometry.type === 'MultiPolygon') {
        // For MultiPolygon: flatten all rings from all polygons
        (feature.geometry as any).coordinates.forEach((polygon: any) => {
          polygon.forEach((ring: any) => {
            allCoords.push(...ring);
          });
        });
      }

      // Calculate bounds from all coordinates
      allCoords.forEach((coord: number[]) => {
        const [lng, lat] = coord;
        minLng = Math.min(minLng, lng);
        maxLng = Math.max(maxLng, lng);
        minLat = Math.min(minLat, lat);
        maxLat = Math.max(maxLat, lat);
      });
    }
  });

  // Check if we found valid bounds
  console.log(`📐 Final bounds calculation: minLng=${minLng}, maxLng=${maxLng}, minLat=${minLat}, maxLat=${maxLat}`);

  if (minLng === Infinity || maxLng === -Infinity || minLat === Infinity || maxLat === -Infinity) {
    console.warn('⚠️ Could not calculate valid bounds from features');
    return undefined;
  }

  const calculatedBounds = {
    north: maxLat,
    south: minLat,
    east: maxLng,
    west: minLng
  };

  // Validate bounds are within South Africa
  const isValidSouthAfricaBounds = (
    calculatedBounds.south >= -35 && calculatedBounds.south <= -22 &&
    calculatedBounds.north >= -35 && calculatedBounds.north <= -22 &&
    calculatedBounds.west >= 16 && calculatedBounds.west <= 33 &&
    calculatedBounds.east >= 16 && calculatedBounds.east <= 33
  );

  if (!isValidSouthAfricaBounds) {
    console.error(`❌ Calculated bounds are outside South Africa:`, calculatedBounds);
    console.log(`🔄 Returning undefined to trigger fallback bounds`);
    return undefined;
  }

  console.log(`✅ Successfully calculated valid South Africa bounds:`, calculatedBounds);
  return calculatedBounds;
};

/**
 * Estimate area from bounds (rough calculation)
 */
export const estimateArea = (bounds?: BoundaryFilterResponse['bounds']): number => {
  if (!bounds) return 0;

  // Simple area estimation in km²
  const latDiff = bounds.north - bounds.south;
  const lngDiff = bounds.east - bounds.west;

  // Convert degrees to km (rough approximation)
  const latKm = latDiff * 111; // 1 degree latitude ≈ 111 km
  const lngKm = lngDiff * 111 * Math.cos((bounds.north + bounds.south) / 2 * Math.PI / 180);

  return Math.abs(latKm * lngKm);
};

/**
 * Load provinces from the administrative boundaries layer
 */
export const loadProvinces = async (): Promise<AdministrativeRegion[]> => {
  const cacheKey = 'provinces';
  const cached = boundaryCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log('Using cached provinces data');
    return cached.data;
  }

  try {
    console.log('🔄 Loading provinces from WFS...');

    const params = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      typeName: BOUNDARY_LAYER,
      maxFeatures: 50, // Limit for provinces
      outputFormat: 'application/json',
      propertyName: 'adm1_id,adm1_en,adm1_pcode' // Only get province fields
    };

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}`, {
      params,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    const geojson = response.data;
    if (geojson && Array.isArray(geojson.features)) {
      // Extract unique provinces
      const provinceMap = new Map<string, AdministrativeRegion>();

      geojson.features.forEach((feature: any) => {
        const props = feature.properties;
        const id = props.adm1_id;
        const name = props.adm1_en;
        const code = props.adm1_pcode;

        if (id && name && !provinceMap.has(id)) {
          provinceMap.set(id, {
            id,
            name,
            code,
            properties: props
          });
        }
      });

      const provinces = Array.from(provinceMap.values());
      provinces.sort((a, b) => a.name.localeCompare(b.name));

      console.log(`✅ Loaded ${provinces.length} provinces`);

      // Cache the result
      boundaryCache.set(cacheKey, {
        data: provinces,
        timestamp: Date.now()
      });

      return provinces;
    } else {
      throw new Error('Invalid GeoJSON response for provinces');
    }
  } catch (error) {
    console.error('Failed to load provinces:', error);

    // Return fallback data
    console.warn('Using fallback provinces data');
    const fallbackProvinces: AdministrativeRegion[] = [
      { id: 'EC', name: 'Eastern Cape', code: 'ZA-EC' },
      { id: 'FS', name: 'Free State', code: 'ZA-FS' },
      { id: 'GT', name: 'Gauteng', code: 'ZA-GT' },
      { id: 'KZN', name: 'KwaZulu-Natal', code: 'ZA-KZN' },
      { id: 'LP', name: 'Limpopo', code: 'ZA-LP' },
      { id: 'MP', name: 'Mpumalanga', code: 'ZA-MP' },
      { id: 'NC', name: 'Northern Cape', code: 'ZA-NC' },
      { id: 'NW', name: 'North West', code: 'ZA-NW' },
      { id: 'WC', name: 'Western Cape', code: 'ZA-WC' }
    ];

    return fallbackProvinces;
  }
};

/**
 * Load districts and metros for a specific province
 * This function will load both:
 * 1. District municipalities (for two-tier system)
 * 2. Metropolitan municipalities (single-tier)
 */
export const loadDistricts = async (provinceId?: string): Promise<AdministrativeRegion[]> => {
  const cacheKey = `districts-${provinceId || 'all'}`;
  const cached = boundaryCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log('Using cached districts/metros data');
    return cached.data;
  }

  try {
    console.log('🔄 Loading districts and metros from WFS...', provinceId ? `for province ${provinceId}` : 'all districts/metros');

    const params: any = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      typeName: BOUNDARY_LAYER,
      maxFeatures: 999,
      outputFormat: 'application/json',
      propertyName: 'adm1_id,adm2_id,adm2_en,adm2_pcode'
    };

    // Add CQL filter for province if provided
    if (provinceId) {
      params.CQL_FILTER = `adm1_id='${sanitizeCQLValue(provinceId)}'`;
    }

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}`, {
      params,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    const geojson = response.data;
    if (geojson && Array.isArray(geojson.features)) {
      // Extract unique districts and metros
      const districtMap = new Map<string, AdministrativeRegion>();

      geojson.features.forEach((feature: any) => {
        const props = feature.properties;
        const id = props.adm2_id;
        const name = props.adm2_en;
        const code = props.adm2_pcode;
        const provinceCode = props.adm1_id;

        // Filter by province if specified
        if (provinceId && provinceCode !== provinceId) return;

        if (id && name && !districtMap.has(id)) {
          // Determine if this is a metropolitan municipality (metro) or district
          // Metro municipalities in South Africa typically don't have "DC" in their code
          const isMetro = !id.startsWith('DC');

          districtMap.set(id, {
            id,
            name,
            code,
            properties: {
              ...props,
              isMetro,
              type: isMetro ? 'metropolitan' : 'district'
            }
          });
        }
      });

      const districts = Array.from(districtMap.values());

      // Sort with metros first, then alphabetically
      districts.sort((a, b) => {
        const aIsMetro = a.properties?.isMetro;
        const bIsMetro = b.properties?.isMetro;

        if (aIsMetro && !bIsMetro) return -1;
        if (!aIsMetro && bIsMetro) return 1;
        return a.name.localeCompare(b.name);
      });

      console.log(`✅ Loaded ${districts.length} districts/metros`, {
        metros: districts.filter(d => d.properties?.isMetro).length,
        districts: districts.filter(d => !d.properties?.isMetro).length
      });

      // Cache the result
      boundaryCache.set(cacheKey, {
        data: districts,
        timestamp: Date.now()
      });

      return districts;
    } else {
      throw new Error('Invalid GeoJSON response for districts/metros');
    }
  } catch (error) {
    console.error('Failed to load districts/metros:', error);
    return [];
  }
};

/**
 * Load municipalities for a specific province/district
 *
 * This function handles two cases:
 * 1. For metropolitan municipalities (metros), it returns the metro itself
 * 2. For district municipalities, it returns the local municipalities within that district
 */
export const loadMunicipalities = async (
  provinceName?: string,
  districtId?: string
): Promise<AdministrativeRegion[]> => {
  const cacheKey = `municipalities-${provinceName || 'all'}-${districtId || 'all'}`;
  const cached = boundaryCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log('Using cached municipalities data');
    return cached.data;
  }

  try {
    console.log('🔄 Loading municipalities from WFS...', { provinceName, districtId });

    // Check if the district is a metro (metropolitan municipality)
    // If it's a metro, we just return the metro itself as there are no local municipalities under it
    if (districtId && !districtId.startsWith('DC')) {
      console.log('Selected district is a metropolitan municipality');

      // For metros, we should use a different layer to get its boundary data
      // For now, we'll look up the metro in our municipal boundaries layer
      const params: any = {
        service: 'WFS',
        version: '1.0.0',
        request: 'GetFeature',
        typeName: BOUNDARY_LAYER,
        maxFeatures: 1,
        outputFormat: 'application/json',
        CQL_FILTER: `adm2_id='${sanitizeCQLValue(districtId)}'`
      };

      const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}`, {
        params,
        timeout: 30000,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        }
      });

      const geojson = response.data;

      if (geojson?.features?.length > 0) {
        const metro = geojson.features[0];
        const props = metro.properties;

        // Return the metro as a single municipality
        const metroMunicipality: AdministrativeRegion = {
          id: props.adm2_id,
          name: props.adm2_en,
          code: props.adm2_pcode,
          properties: {
            ...props,
            isMetro: true,
            type: 'metropolitan'
          }
        };

        // Cache this result
        boundaryCache.set(cacheKey, {
          data: [metroMunicipality],
          timestamp: Date.now()
        });

        console.log('✅ Returned metro as municipality:', metroMunicipality.name);
        return [metroMunicipality];
      }
    }

    // For regular district municipalities, get the local municipalities within them
    const params: any = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      typeName: BOUNDARY_LAYER,
      maxFeatures: 999,
      outputFormat: 'application/json',
      propertyName: 'adm1_en,adm2_id,adm2_en,adm2_pcode'
    };

    // Build CQL filter for both province and district if provided
    const filters: string[] = [];

    if (provinceName) {
      filters.push(`adm1_en='${sanitizeCQLValue(provinceName)}'`);
    }

    if (districtId && districtId.startsWith('DC')) {
      // For district municipalities, we need to filter by the district ID
      // This assumes there's a relationship field in the data
      // If this field doesn't exist, we might need to handle this differently
      filters.push(`district_id='${sanitizeCQLValue(districtId)}'`);
    }

    if (filters.length > 0) {
      params.CQL_FILTER = filters.join(' AND ');
    }

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}`, {
      params,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    const geojson = response.data;
    console.log('🔍 Municipality response structure:', {
      hasData: !!geojson,
      type: typeof geojson,
      keys: geojson ? Object.keys(geojson) : [],
      hasFeatures: geojson?.features ? true : false,
      featuresType: geojson?.features ? typeof geojson.features : 'undefined',
      featuresLength: Array.isArray(geojson?.features) ? geojson.features.length : 'not array'
    });

    if (geojson && Array.isArray(geojson.features)) {
      // Extract unique municipalities from adm2 level
      const municipalityMap = new Map<string, AdministrativeRegion>();

      geojson.features.forEach((feature: any) => {
        const props = feature.properties;
        const id = props.adm2_id;
        const name = props.adm2_en;
        const code = props.adm2_pcode;

        if (id && name && !municipalityMap.has(id)) {
          // Skip district municipalities when looking for local municipalities
          if (districtId && id.startsWith('DC')) {
            return;
          }

          municipalityMap.set(id, {
            id,
            name,
            code,
            properties: {
              ...props,
              type: 'local'  // These are local municipalities
            }
          });
        }
      });

      const municipalities = Array.from(municipalityMap.values());
      municipalities.sort((a, b) => a.name.localeCompare(b.name));

      console.log(`✅ Loaded ${municipalities.length} local municipalities`);

      // Cache the result
      boundaryCache.set(cacheKey, {
        data: municipalities,
        timestamp: Date.now()
      });

      return municipalities;
    } else {
      console.error('Invalid municipality response structure:', {
        responseData: geojson,
        hasFeatures: !!geojson?.features,
        featuresType: typeof geojson?.features
      });
      throw new Error(`Invalid GeoJSON response for municipalities. Expected features array, got: ${typeof geojson?.features}`);
    }
  } catch (error) {
    console.error('Failed to load municipalities:', error);
    return [];
  }
};

/**
 * Load wards for a specific municipality
 * Uses the real "wards (2020)" layer
 *
 * This function handles two scenarios:
 * 1. Wards for metropolitan municipalities
 * 2. Wards for local municipalities
 */
export const loadWards = async (municipalityId?: string): Promise<AdministrativeRegion[]> => {
  // Add debugging
  console.log("loadWards called with municipalityId:", municipalityId);
  debugWardLoading(municipalityId || "");
  debugLayerExistence();
  console.log(`🔍 Loading wards for municipality: ${municipalityId || 'all'}`);

  // Check if municipality ID is provided
  if (!municipalityId) {
    console.log('No municipality selected, cannot filter wards properly');
    return [];
  }

  try {
    // Define the actual ward layer name
    const WARD_LAYER = 'geonode:sa_wards2020';

    // Make a request to fetch the wards from the wards (2020) layer
    const params: Record<string, string | number> = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      typeName: WARD_LAYER,
      outputFormat: 'application/json',
      maxFeatures: 100 // Limit for performance reasons
    };

    // If we have a municipality ID, add a CQL filter
    if (municipalityId) {
      // Determine if this is a metro municipality or local municipality
      const isMetro = !municipalityId.startsWith('DC');

      if (isMetro) {
        // For metros, filter directly by the metro code
        params.CQL_FILTER = `cat_b='${sanitizeCQLValue(municipalityId)}'`;
        console.log('Filtering wards for metropolitan municipality:', municipalityId);
      } else {
        // For local municipalities, we need to filter by the local municipality code or name
        params.CQL_FILTER = `cat_b='${sanitizeCQLValue(municipalityId)}' OR municipali ILIKE '%${sanitizeCQLValue(municipalityId)}%'`;
        console.log('Filtering wards for local municipality:', municipalityId);
      }
    }

    console.log('Fetching wards with params:', params);

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}`, {
      params,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    const geojson = response.data;

    console.log('Ward data response:', {
      hasData: !!geojson,
      type: typeof geojson,
      features: geojson?.features ? geojson.features.length : 'none'
    });

    if (geojson?.features?.length > 0) {
      // Extract ward information from features
      const wards: AdministrativeRegion[] = geojson.features.map((feature: any, index: number) => {
        const props = feature.properties;
        // Use proper ward fields from the wards_2020 layer
        const id = props.wardid || `ward_${index}`;
        const name = props.wardlabel || `Ward ${props.wardno || index}`;
        const code = props.wardno || `W${index}`;

        // Include the municipality name for clarity
        const municipalityName = props.municipali || '';
        const displayName = municipalityName
          ? `${name} (${municipalityName})`
          : name;

        // Extract geometry bounds if available
        let bounds = undefined;
        if (feature.geometry) {
          const coords: number[][] = [];
          // Handle different geometry types
          if (feature.geometry.type === 'MultiPolygon') {
            // Flatten all coordinates
            feature.geometry.coordinates.forEach((poly: any) => {
              poly.forEach((ring: any) => {
                coords.push(...ring);
              });
            });
          } else if (feature.geometry.type === 'Polygon') {
            feature.geometry.coordinates.forEach((ring: any) => {
              coords.push(...ring);
            });
          }

          // Calculate bounds from all coordinates
          if (coords.length > 0) {
            let minLng = Infinity, maxLng = -Infinity;
            let minLat = Infinity, maxLat = -Infinity;

            coords.forEach((coord: number[]) => {
              const [lng, lat] = coord;
              minLng = Math.min(minLng, lng);
              maxLng = Math.max(maxLng, lng);
              minLat = Math.min(minLat, lat);
              maxLat = Math.max(maxLat, lat);
            });

            bounds = {
              north: maxLat,
              south: minLat,
              east: maxLng,
              west: minLng
            };
          }
        }

        return {
          id,
          name: displayName, // Use the enhanced display name
          code,
          properties: {
            ...props,
            bounds,
            municipalityName,
            wardNumber: props.wardno,
            wardLabel: props.wardlabel
          }
        };
      });

      console.log(`✅ Loaded ${wards.length} real wards from wards_2020 layer`);

      // Sort wards by ward number
      return wards.sort((a, b) => {
        const aNum = parseInt(a.properties?.wardNumber || '0');
        const bNum = parseInt(b.properties?.wardNumber || '0');
        return aNum - bNum;
      });
    }

    console.warn('No ward features found in response');
    return [];

  } catch (error) {
    console.error('Failed to load ward data:', error);
    return [];
  }
};

/**
 * Get filtered boundary features for map display
 */
export const getFilteredBoundaryFeatures = async (
  filters: BoundaryFilters
): Promise<BoundaryFilterResponse> => {
  try {
    console.log('🗺️ Loading boundary features for map display...', filters);

    // Determine which layer to use based on the filter level
    // If ward level is requested, use the ward layer, otherwise use municipal boundaries
    const isWardLevel = !!filters.ward;
    const layerToUse = isWardLevel ? 'geonode:wards_2020' : BOUNDARY_LAYER;

    console.log(`Using layer: ${layerToUse} (Ward Level: ${isWardLevel})`);

    // Build CQL filter appropriate for the layer
    const cqlFilter = buildCQLFilter(filters, isWardLevel);

    console.log('🔍 CQL Filter generated:', cqlFilter);
    console.log('🔍 Filter input details:', {
      filters,
      isWardLevel,
      layerToUse,
      hasProvince: !!filters.province,
      hasDistrict: !!filters.district,
      hasMunicipality: !!filters.municipality
    });

    if (!cqlFilter) {
      console.log('⚠️ No CQL filter generated, returning empty results');
      return { features: [], totalCount: 0, isPartial: false };
    }

    const params: Record<string, string | number> = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      typeName: layerToUse,
      outputFormat: 'application/json',
      maxFeatures: 1000,
      CQL_FILTER: cqlFilter
    };

    console.log('🌐 WFS Request params:', params);

    // Convert params to string-only for URL construction
    const urlParams = {
      ...params,
      maxFeatures: params.maxFeatures.toString()
    };
    console.log('🌐 Full request URL:', `${API_CONFIG.OWS_BASE_URL}?${new URLSearchParams(urlParams as Record<string, string>).toString()}`);

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}`, {
      params,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    const geojson = response.data;
    console.log('🔍 Boundary API response status:', response.status);
    console.log('🔍 Boundary API response type:', typeof geojson);
    console.log('🔍 Boundary API response keys:', geojson ? Object.keys(geojson) : 'null');

    if (geojson && Array.isArray(geojson.features)) {
      const features = geojson.features;

      // Debug first few features to understand data structure
      if (features.length > 0) {
        console.log('🔍 Sample feature properties:', {
          firstFeature: features[0]?.properties,
          fieldNames: Object.keys(features[0]?.properties || {}),
          totalFeatures: features.length,
          layerUsed: layerToUse,
          cqlFilterUsed: cqlFilter
        });
      } else {
        console.warn('⚠️ No features returned for filter:', {
          cqlFilter,
          layerUsed: layerToUse,
          filters
        });
      }

      // For ward features, enhance them with better metadata
      if (isWardLevel) {
        features.forEach((feature: GeoJSON.Feature) => {
          const props = feature.properties as Record<string, any>;

          // Add ward bounds to the feature properties for AOIPreviewCard
          if (feature.geometry) {
            let bounds = undefined;
            // Calculate bounds from geometry
            try {
              const coords: number[][] = [];

              // Handle different geometry types
              if (feature.geometry.type === 'MultiPolygon') {
                // Flatten all coordinates
                feature.geometry.coordinates.forEach((poly: any) => {
                  poly.forEach((ring: any) => {
                    coords.push(...ring);
                  });
                });
              } else if (feature.geometry.type === 'Polygon') {
                feature.geometry.coordinates.forEach((ring: any) => {
                  coords.push(...ring);
                });
              }

              // Calculate bounds from all coordinates
              if (coords.length > 0) {
                let minLng = Infinity, maxLng = -Infinity;
                let minLat = Infinity, maxLat = -Infinity;

                coords.forEach((coord: number[]) => {
                  const [lng, lat] = coord;
                  minLng = Math.min(minLng, lng);
                  maxLng = Math.max(maxLng, lng);
                  minLat = Math.min(minLat, lat);
                  maxLat = Math.max(maxLat, lat);
                });

                bounds = {
                  north: maxLat,
                  south: minLat,
                  east: maxLng,
                  west: minLng
                };

                // Add bounds to properties for use in AOIPreviewCard
                feature.properties = {
                  ...props,
                  bounds
                };
              }
            } catch (error) {
              console.error('Error calculating bounds for ward feature:', error);
            }
          }

          // Add a better display name for the ward
          const wardNumber = props?.wardno || '';
          const municipalityName = props?.municipali || '';

          if (feature.properties) {
            feature.properties.displayName = municipalityName
              ? `Ward ${wardNumber} - ${municipalityName}`
              : `Ward ${wardNumber}`;
          }
        });
      }

      console.log(`🔍 Calculating bounds for ${features.length} features...`);
      const bounds = calculateBounds(features);
      console.log(`📐 Calculated bounds:`, bounds);

      const area = estimateArea(bounds);
      console.log(`📏 Estimated area:`, area);

      console.log(`✅ Loaded ${features.length} boundary features for map`);

      return {
        features,
        totalCount: features.length,
        isPartial: features.length >= 1000,
        bounds,
        area
      };
    } else {
      console.error('❌ Invalid GeoJSON response structure:', {
        hasData: !!geojson,
        dataType: typeof geojson,
        hasFeatures: geojson && 'features' in geojson,
        featuresType: geojson && geojson.features ? typeof geojson.features : 'undefined',
        isArray: geojson && Array.isArray(geojson.features),
        sampleData: geojson ? JSON.stringify(geojson).substring(0, 200) + '...' : 'null'
      });
      throw new Error('Invalid GeoJSON response for boundary features');
    }
  } catch (error) {
    console.error('Failed to load boundary features:', error);
    return { features: [], totalCount: 0, isPartial: false };
  }
};

/**
 * Debounced version of getFilteredBoundaryFeatures for real-time filtering
 */
let debouncedTimer: NodeJS.Timeout | null = null;

export const debouncedGetFilteredBoundaryFeatures = (
  filters: BoundaryFilters,
  callback: (result: BoundaryFilterResponse) => void,
  delay: number = 300
) => {
  if (debouncedTimer) {
    clearTimeout(debouncedTimer);
  }

  debouncedTimer = setTimeout(async () => {
    try {
      const result = await getFilteredBoundaryFeatures(filters);
      callback(result);
    } catch (error) {
      console.error('Debounced boundary filter failed:', error);
      callback({ features: [], totalCount: 0, isPartial: false });
    }
  }, delay);
};

/**
 * Get the exact geometry of a selected boundary for precise AOI clipping
 */
export const getSelectedBoundaryGeometry = async (
  filters: BoundaryFilters
): Promise<{
  geometry: GeoJSON.Geometry | null;
  bounds: BoundaryFilterResponse['bounds'];
  feature: GeoJSON.Feature | null;
}> => {
  try {
    console.log('🎯 Getting exact geometry for selected boundary...', filters);

    // Determine which layer to use based on the filter level
    // If ward level is requested, use the ward layer, otherwise use municipal boundaries
    const isWardLevel = !!filters.ward;
    const WARD_LAYER = 'geonode:sa_wards2020';
    const layerToUse = isWardLevel ? WARD_LAYER : BOUNDARY_LAYER;

    console.log(`Using layer: ${layerToUse} for boundary geometry (Ward Level: ${isWardLevel})`);

    // Build CQL filter appropriate for the layer
    const cqlFilter = buildCQLFilter(filters, isWardLevel);

    if (!cqlFilter) {
      console.log('⚠️ No boundary selected for geometry extraction');
      return { geometry: null, bounds: undefined, feature: null };
    }

    const params: Record<string, string | number> = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      typeName: layerToUse,
      outputFormat: 'application/json',
      maxFeatures: 1, // We only need the specific selected boundary
      CQL_FILTER: cqlFilter
    };

    console.log('🌐 Fetching boundary geometry with filter:', cqlFilter);
    console.log('🌐 Full request URL:', `${API_CONFIG.OWS_BASE_URL}?${new URLSearchParams(params as Record<string, string>).toString()}`);

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}`, {
      params,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    console.log('🔍 Boundary geometry API response:', {
      status: response.status,
      dataType: typeof response.data,
      hasFeatures: response.data?.features ? true : false,
      featureCount: response.data?.features?.length || 0
    });

    const geojson = response.data;

    if (geojson && Array.isArray(geojson.features) && geojson.features.length > 0) {
      const feature = geojson.features[0]; // Get the first (and should be only) feature
      const geometry = feature.geometry;
      const bounds = calculateBounds([feature]);

      console.log(`✅ Retrieved geometry for boundary: ${geometry.type}`, {
        coordinates: geometry.coordinates ? 'present' : 'missing',
        coordinatesLength: (geometry as any).coordinates?.length,
        bounds,
        featureProperties: Object.keys(feature.properties || {})
      });

      return {
        geometry,
        bounds,
        feature
      };
    } else {
      console.warn('❌ No boundary feature found for the selected filters:', {
        cqlFilter,
        responseData: geojson
      });

      // Provisional fallback chain: try municipality → district → province when ward geometry is missing

      // Provisional fallback chain: try municipality → district → province when ward geometry is missing
      try {
        console.warn('⚠️ Falling back to coarser boundary geometry due to missing ward geometry.');

        // Helper to attempt a fetch for a given level using existing filters
        const tryLevel = async (level: 'municipality' | 'district' | 'province') => {
          const levelFilters: BoundaryFilters = { ...filters };
          // Turn off ward request
          delete (levelFilters as any).ward;
          // Constrain filters to the requested level (clear more specific keys)
          if (level === 'municipality') {
            // Use municipality if available; otherwise skip
            if (!levelFilters.municipality && !levelFilters.district && !levelFilters.province) return null;
          } else if (level === 'district') {
            delete (levelFilters as any).municipality;
            if (!levelFilters.district && !levelFilters.province) return null;
          } else if (level === 'province') {
            delete (levelFilters as any).municipality;
            delete (levelFilters as any).district;
            if (!levelFilters.province) return null;
          }

          const cql = buildCQLFilter(levelFilters, false);
          if (!cql) return null;

          const altParams: Record<string, string | number> = {
            service: 'WFS',
            version: '1.0.0',
            request: 'GetFeature',
            typeName: BOUNDARY_LAYER,
            outputFormat: 'application/json',
            maxFeatures: 1,
            CQL_FILTER: cql
          };

          const altResp = await axios.get(`${API_CONFIG.OWS_BASE_URL}`, {
            params: altParams,
            timeout: 30000,
            headers: { 'Accept': 'application/json', 'Content-Type': 'application/json' }
          });
          const altGeojson = altResp.data;
          if (altGeojson?.features?.length > 0) {
            const f = altGeojson.features[0];
            return {
              geometry: f.geometry as GeoJSON.Geometry,
              bounds: calculateBounds([f]),
              feature: f as GeoJSON.Feature
            };
          }
          return null;
        };

        // Try municipality, then district, then province
        const muni = await tryLevel('municipality');
        if (muni) return muni;
        const dist = await tryLevel('district');
        if (dist) return dist;
        const prov = await tryLevel('province');
        if (prov) return prov;

        console.warn('⚠️ Fallback chain yielded no geometry; returning null geometry (BBOX-only clipping will be used).');
        return { geometry: null, bounds: undefined, feature: null };
      } catch (fallbackErr) {
        console.error('Fallback geometry retrieval failed:', fallbackErr);
        return { geometry: null, bounds: undefined, feature: null };
      }

    }
  } catch (error) {
    console.error('Failed to get boundary geometry:', error);
    return { geometry: null, bounds: undefined, feature: null };
  }
};

/**
 * Get available boundary values for dropdown population
 */
export const getAvailableBoundaryValues = async (
  level: 'provinces' | 'districts' | 'municipalities' | 'wards',
  parentFilters?: { province?: string; district?: string; municipality?: string }
): Promise<FilterOption[]> => {
  try {
    console.log(`🔍 Loading ${level} options...`, parentFilters);

    let regions: AdministrativeRegion[] = [];

    switch (level) {
      case 'provinces':
        regions = await loadProvinces();
        break;
      case 'districts':
        regions = await loadDistricts(parentFilters?.province);
        break;
      case 'municipalities':
        regions = await loadMunicipalities(
          parentFilters?.province,
          parentFilters?.district
        );
        break;
      case 'wards':
        regions = await loadWards(parentFilters?.municipality);
        break;
    }

    const options: FilterOption[] = regions.map(region => {
      let displayLabel = region.name;

      // Add type information to make the hierarchy clearer
      if (level === 'districts') {
        // For districts, identify metro municipalities vs districts
        const isMetro = region.properties?.isMetro ||
                        region.properties?.type === 'metropolitan' ||
                        !region.id.startsWith('DC');

        // Add type indicator to the label
        displayLabel = isMetro ?
          `${region.name} (Metro Municipality)` :
          `${region.name} (District Municipality)`;
      }

      // For municipalities, distinguish between local and metro
      if (level === 'municipalities') {
        const isMetro = region.properties?.isMetro || region.properties?.type === 'metropolitan';
        if (isMetro) {
          displayLabel = `${region.name} (Metro)`;
        }
      }

      // For wards, use the more detailed property if available
      if (level === 'wards' && region.properties?.wardLabel) {
        displayLabel = region.properties.wardLabel;

        // If we have a municipality name, add it for context
        if (region.properties.municipalityName) {
          displayLabel = `Ward ${region.properties.wardNumber} - ${region.properties.municipalityName}`;
        }
      }

      return {
        value: region.name,
        label: displayLabel,
        // Include additional metadata for filtering
        metadata: {
          id: region.id,
          code: region.code,
          type: region.properties?.type,
          isMetro: region.properties?.isMetro
        }
      };
    });

    console.log(`✅ Loaded ${options.length} ${level} options`);
    return options;
  } catch (error) {
    console.error(`Failed to load ${level} options:`, error);
    return [];
  }
};

