/**
 * AOI Debugging Utilities
 * 
 * This file contains utilities to debug AOI clipping issues
 * including geometry fetching, CQL filter generation, and preview generation
 */

import { getSelectedBoundaryGeometry, BoundaryFilters } from '../services/unifiedBoundaryService';
import { convertGeoJSONToWKT } from './wktConverter';
import { getLayerSchema } from '../services/boundaryService';
import { generateAOIScreenshot } from '../services/geoserverService';

export interface AOIDebugResult {
  step: string;
  success: boolean;
  data?: any;
  error?: string;
  timing?: number;
}

/**
 * Comprehensive AOI debugging function
 */
export const debugAOIClipping = async (
  selectedRegions: {
    provinceName?: string;
    district?: string;
    municipality?: string;
    ward?: string;
  },
  selectedLayers: string[],
  dateRange?: { startDate: string; endDate: string }
): Promise<AOIDebugResult[]> => {
  const results: AOIDebugResult[] = [];
  
  console.log('🔍 === STARTING AOI CLIPPING DEBUG ===');
  console.log('Selected Regions:', selectedRegions);
  console.log('Selected Layers:', selectedLayers);
  console.log('Date Range:', dateRange);

  // Step 1: Test geometry fetching
  try {
    const startTime = Date.now();
    console.log('📍 Step 1: Testing geometry fetching...');
    
    const filters: BoundaryFilters = {
      province: selectedRegions.provinceName,
      district: selectedRegions.district,
      municipality: selectedRegions.municipality,
      ward: selectedRegions.ward
    };
    
    const geometryResult = await getSelectedBoundaryGeometry(filters);
    const timing = Date.now() - startTime;
    
    results.push({
      step: 'geometry_fetch',
      success: !!geometryResult.geometry,
      data: {
        hasGeometry: !!geometryResult.geometry,
        hasFeature: !!geometryResult.feature,
        hasBounds: !!geometryResult.bounds,
        geometryType: geometryResult.geometry?.type,
        bounds: geometryResult.bounds
      },
      timing
    });
    
    console.log('✅ Geometry fetch result:', {
      hasGeometry: !!geometryResult.geometry,
      hasFeature: !!geometryResult.feature,
      hasBounds: !!geometryResult.bounds,
      geometryType: geometryResult.geometry?.type
    });

    // Step 2: Test WKT conversion
    if (geometryResult.geometry) {
      try {
        const startTime2 = Date.now();
        console.log('🔄 Step 2: Testing WKT conversion...');
        
        const wkt = convertGeoJSONToWKT(geometryResult.geometry);
        const timing2 = Date.now() - startTime2;
        
        results.push({
          step: 'wkt_conversion',
          success: !!wkt,
          data: {
            wktLength: wkt.length,
            wktPreview: wkt.substring(0, 100) + '...'
          },
          timing: timing2
        });
        
        console.log('✅ WKT conversion successful, length:', wkt.length);

        // Step 3: Test CQL filter generation
        try {
          const startTime3 = Date.now();
          console.log('🔍 Step 3: Testing CQL filter generation...');
          
          const cqlFilters = await Promise.all(
            selectedLayers.map(async (layerName) => {
              try {
                const schema = await getLayerSchema(layerName);
                const geomField = schema?.geometryField || 'the_geom';
                return `INTERSECTS(${geomField}, ${wkt})`;
              } catch {
                return `INTERSECTS(the_geom, ${wkt})`;
              }
            })
          );
          const timing3 = Date.now() - startTime3;
          
          results.push({
            step: 'cql_generation',
            success: cqlFilters.length > 0,
            data: {
              filterCount: cqlFilters.length,
              filters: cqlFilters.map((filter, i) => ({
                layer: selectedLayers[i],
                filter: filter.substring(0, 50) + '...'
              }))
            },
            timing: timing3
          });
          
          console.log('✅ CQL filters generated:', cqlFilters.length);

          // Step 4: Test screenshot generation
          if (geometryResult.bounds) {
            try {
              const startTime4 = Date.now();
              console.log('📸 Step 4: Testing screenshot generation...');
              
              const screenshotUrl = await generateAOIScreenshot({
                bounds: geometryResult.bounds,
                selectedLayers,
                selectedBasemap: 'osm:osm',
                dimensions: { width: 400, height: 200 },
                format: 'png',
                // Don't use CQL filters for debug to avoid HTTP 431 errors
                cqlFilters: undefined,
                dateRange
              });
              const timing4 = Date.now() - startTime4;
              
              results.push({
                step: 'screenshot_generation',
                success: !!screenshotUrl,
                data: {
                  urlGenerated: !!screenshotUrl,
                  urlLength: screenshotUrl?.length || 0
                },
                timing: timing4
              });
              
              console.log('✅ Screenshot generated successfully');
              
            } catch (error) {
              results.push({
                step: 'screenshot_generation',
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
              });
              console.error('❌ Screenshot generation failed:', error);
            }
          }
          
        } catch (error) {
          results.push({
            step: 'cql_generation',
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          console.error('❌ CQL generation failed:', error);
        }
        
      } catch (error) {
        results.push({
          step: 'wkt_conversion',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        console.error('❌ WKT conversion failed:', error);
      }
    }
    
  } catch (error) {
    results.push({
      step: 'geometry_fetch',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    console.error('❌ Geometry fetch failed:', error);
  }

  console.log('🔍 === AOI CLIPPING DEBUG COMPLETE ===');
  console.table(results);
  
  return results;
};

/**
 * Quick test function to verify GeoServer connectivity
 */
export const testGeoServerConnectivity = async (): Promise<boolean> => {
  try {
    console.log('🌐 Testing GeoServer connectivity...');
    
    // Test basic WFS capabilities
    const response = await fetch('/api/ows/capabilities');
    const success = response.ok;
    
    console.log('GeoServer connectivity:', success ? '✅ Connected' : '❌ Failed');
    return success;
    
  } catch (error) {
    console.error('❌ GeoServer connectivity test failed:', error);
    return false;
  }
};

/**
 * Test individual layer schema detection
 */
export const testLayerSchema = async (layerName: string): Promise<any> => {
  try {
    console.log(`🔍 Testing schema for layer: ${layerName}`);
    
    const schema = await getLayerSchema(layerName);
    console.log(`✅ Schema for ${layerName}:`, schema);
    
    return schema;
    
  } catch (error) {
    console.error(`❌ Schema test failed for ${layerName}:`, error);
    return null;
  }
};
