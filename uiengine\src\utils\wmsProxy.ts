/**
 * Enhanced WMS proxy handler specifically for binary/image responses
 * 
 * This module provides specialized handling for WMS GetMap requests 
 * which return binary image data that needs to be properly streamed.
 */
import axios, { AxiosResponse } from 'axios';
import https from 'https';
import { Readable } from 'stream';

// Create a custom HTTPS agent that ignores certificate validation errors
const httpsAgent = new https.Agent({
  rejectUnauthorized: false
});

/**
 * Response object from streamSecureWmsRequest
 */
export interface WmsStreamResponse {
  status: number;
  headers: any; // Use any to handle complex Axios header types
  data: Readable;
}

/**
 * Stream a WMS response from GeoServer with certificate validation bypassed
 * 
 * @param url - The GeoServer URL
 * @param params - The WMS parameters
 * @returns Response object with headers and a readable stream
 */
export async function streamSecureWmsRequest(url: string, params: Record<string, any>): Promise<WmsStreamResponse> {
  console.log(`🌐 Streaming WMS request to ${url}`);

  // Safely log params without circular references
  try {
    const safeParams: Record<string, any> = {};
    for (const [key, value] of Object.entries(params)) {
      if (typeof value === 'object' && value !== null) {
        safeParams[key] = '[Object]';
      } else {
        safeParams[key] = value;
      }
    }
    console.log(`🌐 WMS Params:`, JSON.stringify(safeParams));

    // Special logging for CQL_FILTER to debug clipping issues
    if (params.CQL_FILTER) {
      console.log(`✂️ CQL_FILTER detected in WMS request:`, params.CQL_FILTER);
      console.log(`🎯 This should enable precise geometry clipping instead of BBOX`);
    } else {
      console.log(`📦 No CQL_FILTER in WMS request - using BBOX clipping or no clipping`);
    }
  } catch (e) {
    console.log(`🌐 WMS Params: [Unable to stringify - contains circular references]`);
  }

  try {
    const response: AxiosResponse<Readable> = await axios({
      method: 'get',
      url: url,
      params: params,
      responseType: 'stream',
      httpsAgent
    });

    console.log(`🌐 WMS stream response received:`, {
      status: response.status,
      headers: response.headers,
    });

    return {
      status: response.status,
      headers: response.headers as any, // Type assertion to handle Axios header complexity
      data: response.data
    };
  } catch (error: any) {
    console.error(`🌐 WMS stream request failed:`, error.message);
    if (error.response) {
      console.error(`🌐 Status: ${error.response.status}`);
      console.error(`🌐 Headers:`, error.response.headers);
      
      // If GeoServer returned an error with XML content, convert to string for debugging
      if (error.response.data) {
        const chunks: Buffer[] = [];
        await new Promise<void>((resolve, reject) => {
          error.response.data.on('data', (chunk: Buffer) => chunks.push(chunk));
          error.response.data.on('end', () => resolve());
          error.response.data.on('error', reject);
        });
        
        const buffer = Buffer.concat(chunks);
        console.error(`🌐 Error response body:`, buffer.toString('utf8'));
      }
    }
    throw error;
  }
}
